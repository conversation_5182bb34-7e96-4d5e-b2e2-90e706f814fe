{"pegaBodyResponse": {"view": {"reference": "", "validationMessages": "", "viewID": "DettagliResidenza", "visible": true, "titleFormat": "", "name": "DettagliResidenza", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"view": {"reference": "", "validationMessages": "", "viewID": "AnalyticsHidden", "visible": true, "titleFormat": "", "name": "AnalyticsHidden", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202212131640340513751", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "analyticsPageFields", "funnel_name": "Prodotto Unico", "page_name": "unico:residenza", "proposal_source": "web", "user_fiscal_code": "", "funnel_type": "nuovo", "postsale_contract_operation_type": ""}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}, {"view": {"reference": "", "validationMessages": "", "viewID": "Intestazione", "visible": true, "titleFormat": "", "name": "Intestazione", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202212131640340513751", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"customType": "Header", "background": "Secondary"}}}, {"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "Standard", "testID": "202301131218420210633"}, "value": "La tua casa", "testID": "202301131218420210633"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore", "iconSource": "image", "iconImage": "webwb/pymenuleftarrow.png"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Standard", "formatType": "text", "showReadOnlyValidation": "false", "iconSource": "image", "iconImage": "webwb/pymenuleftarrow.png"}], "actionSets": [{"actions": [{"action": "takeAction", "actionProcess": {"actionName": "Indietro"}}], "events": [{"event": "click"}]}], "type": "pxIcon"}, "label": "Image", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateGeneric", "labelFormat": "Standard", "disabled": false, "testID": "202212141106470503509", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"componentID": "_back", "size": "S", "resource": "arrowLeft", "type": "icon"}, "showLabel": false}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}, {"view": {"reference": "", "validationMessages": "", "viewID": "BarraDiAvanzamento", "visible": true, "titleFormat": "", "name": "BarraDiAvanzamento", "appliesTo": "UG-Ins-PU-Work-Quotazione", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false}], "actionSets": [], "type": "pxHidden"}, "label": "", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateInputBox", "labelFormat": "Standard", "disabled": false, "testID": "202212131640340513751", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateInputBox", "customAttributes": {"currentStep": "0", "labelFormatDeselected": "TEXT APP GRL13 WEB GRL14 GRL14 GRL14", "customType": "Stepper", "labelFormatPreviousStep": "TEXT APP BDL13 WEB BDL14 BDL14 BDL14", "progress": "29", "style": "StepperPU", "labelFormatCurrentStep": "TEXT APP BDM13 WEB BDM14 BDM14 BDM14", "labelsStepper": "Preventivo|Carrello|Acquisto"}}}], "groupFormat": "CustomComponent", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"view": {"reference": "Ambito", "validationMessages": "", "viewID": "DettagliResidenza", "visible": true, "titleFormat": "", "name": "DettagliResidenza", "appliesTo": "UG-Ins-PU-Data-Ambito", "groups": [{"view": {"reference": "Ambito.Bene", "validationMessages": "", "viewID": "DettagliResidenza", "visible": true, "titleFormat": "", "name": "DettagliResidenza", "appliesTo": "UG-Ins-PU-Data-Bene", "groups": [{"view": {"reference": "Ambito.Bene.Casa", "validationMessages": "", "viewID": "DettagliResidenza", "visible": true, "titleFormat": "", "name": "DettagliResidenza", "appliesTo": "UG-Ins-PU-Data-Casa", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "Text APP BDM16 WEB BDB20C BDB20C BDB16C", "testID": "202302131222180692405"}, "value": "Hai la residenza in questa abitazione?", "testID": "202302131222180692405"}}, {"caption": {"columnImportance": "", "visible": false, "captionFor": "", "control": {"format": "Text APP BDM16 WEB BDB20C BDB20C BDB16C", "testID": "202302131222180692273"}, "value": "Il membro del nucleo è residente in questa casa?", "testID": "202302131222180692273"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"orientation": "vertical", "listSource": "locallist", "textAlign": "Left", "wrapAfter": 3, "tooltip": "", "minChars": "", "modeType": "editable", "controlFormat": "", "options": [{"value": "Si", "key": "Si"}, {"value": "No", "key": "No"}], "specifySize": "auto", "formatType": "text", "obfuscated": false, "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false, "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "refresh", "refreshFor": "RGV0dGFnbGlSZXNpZGVuemFVRy1JbnMtUFUtRGF0YS1DYXNhLkRpbW9yYVJlc2lkdWFsZTIwMjMwMTAyMTUxMjMwMDcwMzgz"}], "events": [{"event": "change"}]}], "type": "pxRadioButtons"}, "label": "", "type": "Text", "required": true, "validateAs": "", "reference": "Ambito.Bene.Casa.DimoraResiduale", "labelFormat": "", "disabled": false, "testID": "20230102151230070383", "value": "Si", "maxLength": 0, "expectedLength": "", "fieldID": "DimoraResiduale", "customAttributes": {"style": "FlatCardInverted"}}}], "groupFormat": "Col TL TC 32 16 16 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}], "title": ""}}], "title": ""}}, {"view": {"viewID": "IndirizzoDiResidenzaAssicurato", "visible": false, "name": "IndirizzoDiResidenzaAssicurato"}}, {"view": {"viewID": "BoxIndirizzoDiResidenzaAssicurato", "visible": false, "name": "BoxIndirizzoDiResidenzaAssicurato"}}, {"view": {"reference": "Ambito", "validationMessages": "", "viewID": "UsoImmobile", "visible": true, "titleFormat": "", "name": "UsoImmobile", "appliesTo": "UG-Ins-PU-Data-Ambito", "groups": [{"view": {"reference": "Ambito.Bene", "validationMessages": "", "viewID": "UsoImmobile", "visible": true, "titleFormat": "", "name": "UsoImmobile", "appliesTo": "UG-Ins-PU-Data-Bene", "groups": [{"view": {"reference": "Ambito.Bene.Casa", "validationMessages": "", "viewID": "UsoImmobile", "visible": true, "titleFormat": "", "name": "UsoImmobile", "appliesTo": "UG-Ins-PU-Data-Casa", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "Text APP BDM 16 WEB BDB20C BDB20C BDB16C", "testID": "202212271254490769430"}, "value": "È la casa dove vivi abitualmente?", "testID": "202212271254490769430"}}, {"caption": {"columnImportance": "", "visible": false, "captionFor": "", "control": {"format": "Text APP BDM 16 WEB BDB20C BDB20C BDB16C", "testID": "202212271254490769430"}, "value": "È la casa dove vive abitualmente?", "testID": "202212271254490769430"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"dataPagePrompt": "label", "groupOrder": "asc", "orientation": "vertical", "listSource": "datapage", "textAlign": "Left", "wrapAfter": 3, "tooltip": "", "enableGrouping": false, "groupBy": "", "minChars": "", "dataPageParams": [{"valueReference": {"reference": "pyWorkPage.GestioneProcesso.IdTemplate", "lastSavedValue": "181cbdfd-e9e6-4def-b33e-3eba5afa5dee"}, "name": "id"}, {"name": "detailLevel"}, {"valueReference": {"reference": "pyWorkPage.Ambito.CodiceProdottoWPT", "lastSavedValue": "PUCASA"}, "name": "<PERSON><PERSON><PERSON>"}, {"valueReference": {"reference": "pyWorkPage.Ambito.Bene.DescrizioneBeneWPT", "lastSavedValue": ""}, "name": "<PERSON><PERSON>"}, {"valueReference": {"reference": "pyWorkPage.Ambito.Chiave", "lastSavedValue": "CASA APRILE 25"}, "name": "Chiave"}, {"valueReference": {"reference": ".UtenteProprietario", "lastSavedValue": "1"}, "name": "UtenteProprietario"}, {"valueReference": {"reference": ".UtenteAffittuario", "lastSavedValue": "1"}, "name": "UtenteAffittuario"}], "modeType": "editable", "dataPageValue": "Valore", "controlFormat": "", "dataPageTooltip": "", "loadMode": "auto", "options": [{"value": "Sì", "key": "1"}, {"value": "No, ci vivo saltuariamente", "key": "2"}, {"value": "No, la affitto a terzi", "key": "3"}], "dataPageID": "D_DominioTipoDimora_Editable", "specifySize": "auto", "formatType": "text", "obfuscated": false, "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false, "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "postValue"}], "events": [{"event": "change"}]}], "type": "pxRadioButtons"}, "label": "", "type": "Text", "required": true, "validateAs": "", "reference": "Ambito.Bene.Casa.TipoDimora", "labelFormat": "", "disabled": false, "testID": "20230102151230070383", "value": "1", "maxLength": 0, "expectedLength": "", "fieldID": "TipoDimora", "customAttributes": {"style": "FlatCardInverted"}}}], "groupFormat": "Col TL TC 32 16 16 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}], "title": ""}}], "title": ""}}, {"view": {"reference": "Ambito", "validationMessages": "", "viewID": "PresenzaPertinenze", "visible": true, "titleFormat": "", "name": "PresenzaPertinenze", "appliesTo": "UG-Ins-PU-Data-Ambito", "groups": [{"view": {"reference": "Ambito.Bene", "validationMessages": "", "viewID": "PresenzaPertinenze", "visible": true, "titleFormat": "", "name": "PresenzaPertinenze", "appliesTo": "UG-Ins-PU-Data-Bene", "groups": [{"view": {"reference": "Ambito.Bene.Casa", "validationMessages": "", "viewID": "PresenzaPertinenze", "visible": true, "titleFormat": "", "name": "PresenzaPertinenze", "appliesTo": "UG-Ins-PU-Data-Casa", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"caption": {"columnImportance": "", "visible": true, "captionFor": "", "control": {"format": "Text APP BDM 16 WEB BDB20C BDB20C BDB16C", "testID": "202212271254490769430"}, "value": "Hai pertinenze o dipendenze (es. box, garage, cantine, ecc.) al piano interrato o seminterrato?", "testID": "202212271254490769430"}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore", "iconSource": "image", "iconImage": "dsmimages/pzInformation.png"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Standard", "formatType": "text", "showReadOnlyValidation": "false", "iconSource": "image", "iconImage": "dsmimages/pzInformation.png"}], "actionSets": [{"actions": [{"action": "localAction", "actionProcess": {"localAction": "TooltipPertinenze", "localActionFormat": "", "customTemplate": "pzModalTemplate", "target": "overlay"}}], "events": [{"event": "click"}]}], "type": "pxIcon"}, "label": "Image", "type": "Text", "required": false, "validateAs": "", "reference": "Ambito.Bene.Casa.pyTemplateGeneric", "labelFormat": "Standard", "disabled": false, "testID": "202301021132400354827", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateGeneric", "customAttributes": {"tooltipDirection": "TooltipLU", "onlyIcon": "true", "size": "S", "resource": "info", "tooltipID": "tooltip1", "type": "icon"}, "showLabel": false}}], "groupFormat": "Row SBC 0 0", "layoutFormat": "SIMPLELAYOUT", "title": ""}}, {"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"dataPagePrompt": "label", "groupOrder": "asc", "orientation": "horizontal", "listSource": "datapage", "textAlign": "Left", "wrapAfter": 3, "tooltip": "", "enableGrouping": false, "groupBy": "", "minChars": "", "dataPageParams": [{"valueReference": {"reference": "pyWorkPage.GestioneProcesso.IdTemplate", "lastSavedValue": "181cbdfd-e9e6-4def-b33e-3eba5afa5dee"}, "name": "id"}, {"name": "detailLevel"}, {"valueReference": {"reference": "pyWorkPage.Ambito.CodiceProdottoWPT", "lastSavedValue": "PUCASA"}, "name": "<PERSON><PERSON><PERSON>"}, {"valueReference": {"reference": "pyWorkPage.Ambito.Bene.DescrizioneBeneWPT", "lastSavedValue": ""}, "name": "<PERSON><PERSON>"}, {"valueReference": {"reference": "pyWorkPage.Ambito.Chiave", "lastSavedValue": "CASA APRILE 25"}, "name": "Chiave"}], "modeType": "editable", "dataPageValue": "Valore", "controlFormat": "", "dataPageTooltip": "", "loadMode": "auto", "options": [{"value": "No", "key": "0"}, {"value": "Sì", "key": "1"}], "dataPageID": "D_DominioPertinenze", "specifySize": "auto", "formatType": "text", "obfuscated": false, "maxChars": ""}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "formatType": "text", "obfuscated": false, "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "postValue"}], "events": [{"event": "change"}]}], "type": "pxRadioButtons"}, "label": "", "type": "Text", "required": true, "validateAs": "", "reference": "Ambito.Bene.Casa.PresenzaPertinenze", "labelFormat": "", "disabled": false, "testID": "20230102151230070383", "value": "1", "maxLength": 0, "expectedLength": "", "fieldID": "PresenzaPertinenze", "customAttributes": {"style": "FlatCardInverted"}}}], "groupFormat": "Col TL TC 32 16 16 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}}], "title": ""}}], "title": ""}}, {"layout": {"visible": true, "titleFormat": "h2", "containerFormat": "NOHEADER", "groups": [{"field": {"validationMessages": "", "visible": true, "labelReserveSpace": false, "readOnly": false, "control": {"modes": [{"tooltip": "", "modeType": "ignore"}, {"tooltip": "", "modeType": "readOnly", "autoPrepend": "", "autoAppend": "", "controlFormat": "Full Positive Button", "formatType": "text", "showReadOnlyValidation": "false"}], "actionSets": [{"actions": [{"action": "finishAssignment"}], "events": [{"event": "click"}]}], "type": "px<PERSON><PERSON><PERSON>", "label": "Prosegui"}, "label": "<PERSON><PERSON>", "type": "Text", "required": false, "validateAs": "", "reference": "pyTemplateButton", "labelFormat": "Standard", "disabled": false, "testID": "202212271254490769947", "value": "", "maxLength": 0, "expectedLength": "", "fieldID": "pyTemplateButton", "customAttributes": {"loadingList": "", "submitButton": "true", "specialLoading": "false", "position": "bottom", "paddingApp": "0 48 0 0"}, "showLabel": false}}], "groupFormat": "CustomPosition", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "Col TL TC 32 32 32 32 MW496", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 0 D 0 0 0 16 T 0 0 0 16 M 0 0 0 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "StackedLayout", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "groupFormat": "P 20 32 D 0 32 T 0 32 M 16 16", "layoutFormat": "SIMPLELAYOUT", "title": ""}}], "title": ""}, "actionID": "DettagliResidenza", "caseID": "UG-INS-PU-WORK-QUOTAZIONE Q-36620", "name": "Dettagli sulla residenza"}, "metaBodyResponse": {"assignmentId": "ASSIGN-WORKLIST UG-INS-PU-WORK-QUOTAZIONE Q-36620!RACCOLTADATICASA", "actionId": "DettagliResidenza", "caseId": "UG-INS-PU-WORK-QUOTAZIONE Q-36620"}, "analyticsBodyResponse": {}}