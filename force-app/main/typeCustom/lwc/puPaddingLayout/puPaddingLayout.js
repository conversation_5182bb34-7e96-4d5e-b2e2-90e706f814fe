import { LightningElement, api } from 'lwc';
import { utilsPega } from 'c/utilsPega';

export default class PuPaddingLayout extends LightningElement {
    @api groupFormat;

    get combinedPaddingStyle() {
        const paddingStyles = utilsPega.layout.parseResponsivePaddingFormat(this.groupFormat);
        let styleString = '';
        for (const key in paddingStyles) {
            styleString += `${key}: ${paddingStyles[key]};`;
        }
        return styleString;
    }
}
