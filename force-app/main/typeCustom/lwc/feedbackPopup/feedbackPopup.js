import { LightningElement, api } from 'lwc';

export default class FeedbackPopup extends LightningElement {
    //@api visualizzaFeedbackPopup = false;
    @api contenutoPopup = '';

    // Se viene passato "contenuto" (ad esempio come numero) si imposta una chiusura automatica dopo 3 secondi
    @api
    set contenuto(data) {
        // Puoi elaborare 'data' come occorre e poi impostare eventualmente contenutoPopup
        window.setTimeout(() => {
            this.onCloseClickHandler();
        }, 3000);
    }

    get contenuto() {
        return this.contenutoPopup;
    }

    onCloseClickHandler() {
        this.visualizzaFeedbackPopup = false;
        const closeEvent = new CustomEvent('close');
        this.dispatchEvent(closeEvent);
    }
}