.FeedbackContainer {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 300px;
    max-width: 90vw;
    height: 80px;
    background-color: white;
    border: 1px solid var(--border-card-disabled, #ccc);
    border-radius: 24px;
    overflow: hidden;
    gap: 16px;
    position: fixed;
    z-index: 100;
    left: 50%;
    transform: translateX(-50%);
    top: -100px;
    animation-duration: 0.2s;
    animation-fill-mode: forwards;
    animation-name: animazioneDiscesa;
}

@keyframes animazioneDiscesa {
    from {
        top: -100px;
    }
    to {
        top: 60px;
    }
}

.FeedbackCheckmark {
    display: block;
    position: relative;
    background-color: var(--check-green-color, #00ff00);
    height: 100%;
    width: 60px;
    flex-shrink: 0;
}

.FeedbackCheckmark::before {
    content: "";
    aspect-ratio: 1 / 1;
    display: block;
    width: 20px;
    background-color: transparent;
    border: 2px solid white;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.FeedbackCheckmark::after {
    content: "";
    aspect-ratio: 2 / 1;
    display: block;
    width: 6px;
    background-color: transparent;
    border: 2px solid white;
    border-top: 0;
    border-right: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
}

.FeedbackClose {
    aspect-ratio: 1 / 1;
    display: block;
    flex-shrink: 0;
    width: 24px;
    position: relative;
    cursor: pointer;
    margin-left: auto;
    margin-right: 20px;
}

.FeedbackClose::before,
.FeedbackClose::after {
    content: "";
    background-color: var(--blue-primary, #0070d2);
    width: 2px;
    border-radius: 10px;
    height: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
}

.FeedbackClose::before {
    transform: translate(-50%, -50%) rotate(-45deg);
}

.FeedbackClose::after {
    transform: translate(-50%, -50%) rotate(45deg);
}