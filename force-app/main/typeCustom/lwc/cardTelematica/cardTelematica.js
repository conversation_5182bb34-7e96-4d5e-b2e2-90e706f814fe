import { utils } from 'c/utils';
import { LightningElement, api } from 'lwc';

export default class CardTelematica extends LightningElement {
  _field;
  _groups;

  @api
  get groups() {
    return this._groups;
  }
  set groups(value) {
    this._groups = value;
    this.initializeData();
  }

  @api
  get field() {
    return this._field;
  }
  set field(value) {
    this._field = value;
    this.initializeData();
  }

  get checkbox() {
    return utils.getFirstFieldInGroupsByType(this._groups, 'pxCheckbox');
  }

  get nomeGaranzia() {
    const caption = utils.getCaptionByCaptionFor(this._groups, 'NomeGaranzia');
    const value = utils.captionToValue(caption)?.value || '';
    return value;
  }

  get labelFrazionamentoCanone() {
    const caption = utils.getCaptionByCaptionFor(this._groups, '');
    const value = utils.captionToValue(caption)?.value || '';
    return value;
  }

  get descrizioneTelematica() {
    const paragraph = utils.getParagraphByParagraphId(this._groups, 'LabelDettagliUnibox');
    //const value = paragraph?.value || '';
    const value = {
      value: paragraph?.value || '',
    };

    return value;
  }

  get isChecked() {
    return this.checkboxTelematica?.value === 'true';
  }

  get isDisabled() {
    return this.checkbox?.disabled;
  }

  get premioRataScontato() {
    const result = utils.getFirstCurrencyByFieldId(this._groups, 'RataPremioLordoScontato');
    if (result) {
      const value = `${result.value}${result.symbol}`;
      return value;
    }
  }

  initializeData() {
    if (this._field?.customAttributes) {
      this.CardTelematicaHeader = this._field.customAttributes.CardTelematicaHeader;
    }

    if (this.checkbox) {
      this.checked = this.checkbox.value === 'true';
    }
  }
}
