<template>
    <div class="CaTelematica">
        <span class="CardTelematicaHeader">
            <template if:true={nomeGaranzia}>
                <c-custom-text-styles content={nomeGaranzia} text-css="STYLE TEXT APP BDL13 WEB BDL14 BDL14 BDL14">
                </c-custom-text-styles>
            </template>
            <template if:true={checkbox}>
                <c-field-checkbox field={checkbox}>
                </c-field-checkbox>
            </template>
        </span>
        <div class="CardTelematicaFrazionamento">
            <c-custom-text-styles content={premioRataScontato} text-css="STYLE TEXT APP BDL13 WEB BDL14 BDL14 BDL14">
            </c-custom-text-styles>
            <template if:true={canoneTelematica}>
                <c-field data={canoneTelematica}></c-field>
            </template>
            <template if:true={labelFrazionamentoCanone}>
                <c-custom-text-styles content={labelFrazionamentoCanone}
                    text-css="STYLE TEXT APP BDL13 WEB BDL14 BDL14 BDL14">
                </c-custom-text-styles>
            </template>
            <!--template if:true={descrizioneTelematica}>
                <c-custom-text-styles
                  content={descrizioneTelematica}
                  text-css="STYLE TEXT APP BDL13 WEB BDL14 BDL14 BDL14">
                </c-custom-text-styles>
            </template-->
            <template if:true={descrizioneTelematica}>
                <c-field-paragraph field={descrizioneTelematica}
                    public-pega-style="STYLE TEXT APP BDL13 WEB BDL14 BDL14 BDL14"></c-field-paragraph>
            </template>
        </div>
    </div>
</template>