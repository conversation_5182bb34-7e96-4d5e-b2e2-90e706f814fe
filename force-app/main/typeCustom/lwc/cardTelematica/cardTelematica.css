
.CaTelematica {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 20px;
    gap: 16px;
    border: 2px solid var(--secondary-lightest);
    border-radius: 8px;
    margin: 8px 0;
}

.CardTelematicaHeader {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
    color: #2a5981;
    font-size: 20px;
}

.HeaderCheckbox {
    aspect-ratio: 1 / 1;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    width: 24px;
    cursor: pointer;
    border: 2px solid var(--blue-primary);
    border-radius: 50%;
}

.HeaderCheckbox.disabled {
    border-color: var(--check-disabled-color);
    cursor: default;
    background-color: white;
}

.HeaderCheckbox.checked {
    border: 0;
    background-color: var(--check-green-color);
}

.HeaderCheckbox.checked.disabled {
    background-color: var(--check-green-disabled-color);
}

.HeaderCheckbox.checked:after {
    content: "";
    aspect-ratio: 5 / 3;
    display: block;
    width: 40%;
    transform: rotateZ(-45deg);
    border-bottom: 2px solid white;
    border-left: 2px solid white;
}

.CardTelematicaFrazionamento {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #084175;
    font-size: 15px;
}

.DescrizioneTelematica {
    align-self: center;
    color: #084175;
    font-size: 13px;
    padding: 5px;
}

.ValorePremioLordoScontato {
    font-size: 20px;
    font-family: var(--font-family-default);
    font-weight: bold;
}