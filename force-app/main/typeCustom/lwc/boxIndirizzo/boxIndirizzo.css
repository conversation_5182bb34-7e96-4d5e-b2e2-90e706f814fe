.address-layout {
  display: flex;
  align-items: flex-start;
  flex-direction: row;
  gap: 5px;
}

.layout-box-indirizzo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 16px;
}

.layout-text-address {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.iconBox {
 /* background-color: violet; */
 margin-left: 8px;
}

.card-box {
  border: solid 2px #b0c4de; /* Colore medio azzurro chiaro */
  background-color: white;
}

@media (max-width: 768px) { /* Adatta la query media per il breakpoint mobile */
  .card-box {
    padding: 12px 16px;
  }
}

@media screen and (max-width: 480px) {
  .layout-text-address {
    flex-direction: column;
    gap: 5px;
  }
}

@media screen and (min-width: 481px) and (max-width: 1024px) {
  .layout-text-address {
    flex-direction: column;
    gap: 5px;
  }
}

@media screen and (min-width: 1025px) {
  .layout-text-address {
    flex-direction: column;
    gap: 5px;
  }
}

.card-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.Text-responsive-medium,
.Text-responsive-L,
.Text-responsive-medium-M {
  font-family: var(--font-family-medium); 
  color: var(--color-darker); 
}

@media (max-width: 768px) { 
  .Text-responsive-medium,
  .Text-responsive-L,
  .Text-responsive-medium-M {
    font-size: var(--font-text-responsive-mobile-size); 
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .Text-responsive-medium,
  .Text-responsive-L,
  .Text-responsive-medium-M {
    font-size: var(--font-text-responsive-tablet-size); 
  }
}

@media (min-width: 1025px) { 
  .Text-responsive-medium,
  .Text-responsive-L,
  .Text-responsive-medium-M {
    font-size: var(--font-text-responsive-desktop-size);
  }
}

.Text-responsive,
.Address-Text {
  font-family: "Arial, sans-serif"; /* Sostituito $font-family-default */
  color: var(--light-blue-color); 
}

@media (max-width: 768px) { /* Mobile breakpoint */
  .Text-responsive,
  .Address-Text {
    font-size: 14px; /* Sostituito $font-text-responsive-mobile-size */
  }
}

@media (min-width: 769px) and (max-width: 1024px) { /* Tablet breakpoint */
  .Text-responsive,
  .Address-Text {
    font-size: 16px; /* Sostituito $font-text-responsive-tablet-size */
  }
}

@media (min-width: 1025px) { /* Desktop breakpoint */
  .Text-responsive,
  .Address-Text {
    font-size: 18px; /* Sostituito $font-text-responsive-desktop-size */
  }
}

.paddingApp {
  padding: 16px 16px;
}
