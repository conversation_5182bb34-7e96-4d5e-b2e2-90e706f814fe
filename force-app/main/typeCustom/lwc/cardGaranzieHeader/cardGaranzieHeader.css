.CardGaranzieHeaderContainer {
    display: flex;
    flex-direction: row;
    gap: 8px;
  }
  
  .CardGaranzieHeaderContainer > .policyNameLabel {
    display: flex;
    color: var(--blue-primary);
    font-family: var(--font-family-medium);
    font-size: 16px;
  }

   .captionTextLabel {
    font-weight: 800;
    font-size: 14px;
    color: #3a7db5;
    margin-top: 4px;
    display: flex;
   }
  
  .CardGaranzieHeaderContainer > .RightSideContainer {
    display: flex;
    margin-left: auto;
    gap: 8px;
  }
  
  .CardGaranzieHeaderContainer > .RightSideContainer > .RequiredLabel {
    color: #9b9b9b;
    font-family: var(--font-family-medium);
    font-size: 16px;
  }
  
  .CardGaranzieHeaderContainer .visibleMobile {
    --statusMobile: 'false';
  }