.AssurancePackageContainer {
  --AssurancePackageContainerSize: 470px;
  --HeaderFontSize: 20px;
  --PackageNameSize: 28px;
  --PrezzoAlMeseSize: 40px;
  --LabelAlMeseSize: 20px;
  --PrezzoAnnoSize: 18px;
  --PrezzoAnnoScontatoSize: 22px;
  --LabelAllAnnoSize: 14px;
  --ScontoSize: 18px;

  --AssurancePackageContainerColor: #8ab5d1; /* $secondary-opaque */
  --AssurancePackageRibbonColor: #e2f0f9; /* $secondary-lightest */

  display: flex;
  flex-direction: column;
  width: var(--AssurancePackageContainerSize);
}

@media screen and (max-width: 767px) {
  .AssurancePackageContainer {
      --AssurancePackageContainerSize: 100%;
      --HeaderFontSize: 10px;
      --PackageNameSize: 16px;
      --PrezzoAlMeseSize: 20px;
      --LabelAlMeseSize: 11px;
      --PrezzoAnnoSize: 12px;
      --PrezzoAnnoScontatoSize: 14px;
      --LabelAllAnnoSize: 11px;
      --ScontoSize: 14px;
  }
}

@media screen and (min-width: 768px) and (max-width: 1024px) {
  .AssurancePackageContainer {
      --AssurancePackageContainerSize: 100%; 
      --HeaderFontSize: 16px;
      --PackageNameSize: 24px;
      --PrezzoAlMeseSize: 30px;
      --LabelAlMeseSize: 14px;
      --PrezzoAnnoSize: 14px;
      --PrezzoAnnoScontatoSize: 18px;
      --LabelAllAnnoSize: 12px;
      --ScontoSize: 16px;
  }
}

.AssurancePackageHeader {
  display: flex;
  flex-direction: row;
  justify-content: center;
  text-transform: uppercase;
  font-family: var(--font-family-default);
  font-weight: 600;
  font-size: var(--HeaderFontSize);
  background-color: #193a56; /* $blue-primary */
  color: white;
  padding: 4px 16px;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  position: relative;
}

.AssurancePackageHeader::after {
  content: "";
  width: 100%;
  background-color: inherit;
  height: 20px;
  position: absolute;
  top: 100%;
}

.AssurancePackageBody {
  display: flex;
  flex-direction: column;
  background-color: var(--AssurancePackageContainerColor);
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding: 16px;
  color: white;
  gap: 12px;
  z-index: 10;
}

.AssurancePackageBodyHead {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}

.PackageName {
  text-transform: uppercase;
  font-weight: 900;
  font-size: var(--PackageNameSize);
}

.AssurancePackageContent {
  display: flex;
  flex-direction: column;
}

.PrezzoAlMese {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  font-family: 'Unipol Bold';
  font-weight: 600;
  font-size: var(--PrezzoAlMeseSize);
}

.LabelAlMese {
  font-family: var(--font-family-default);
  font-weight: 100;
  font-size: var(--LabelAlMeseSize);
}

.LabelAllAnno {
  font-family: var(--font-family-default);
  font-weight: 100;
  font-size: var(--LabelAllAnnoSize);
}

.LabelSconto {
  margin: 0 4px;
}

.PrezzoAllAnno {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  gap: 4px;
}

@media screen and (max-width: 767px) {
  .PrezzoAllAnno {
      flex-direction: column;
      align-items: flex-start;
  }
}

.PrezzoAnno {
  font-family: var(--font-family-default);
  font-weight: 100;
  text-decoration: line-through;
  font-size: var(--PrezzoAnnoSize);
}

.ValorePremioLordo {
  text-decoration: line-through;
  margin: 0 4px;
  font-family: var(--font-family-default);
  font-weight: bold;
  font-size: 20px;
}

.ValorePremioLordoScontato {
  margin: 0 4px;
  font-family: var(--font-family-default);
  font-weight: bold;
  font-size: 28px;
}

.PrezzoAnnoScontato {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  gap: 4px;
  font-family: var(--font-family-default);
  font-weight: bold;
  font-weight: 600;
  font-size: var(--PrezzoAnnoScontatoSize);
}

.LabelAllAnno {
  font-family: var(--font-family-default);
  font-weight: 100;
  font-size: var(--LabelAllAnnoSize);
}

.AssurancePackageSconto {
  display: flex;
  flex-direction: row;
  justify-content: center;
  padding: 4px 16px;
  font-weight: 900;
  font-size: var(--ScontoSize);
  color: #193a56; /* $blue-primary */
  background-color: var(--AssurancePackageRibbonColor);
}

.AssurancePackageSconto[data-sconto-assente="true"] {
  user-select: none !important;
  color: transparent !important;
  background-color: var(--AssurancePackageContainerColor) !important;
}

.AssurancePackageFooter {
  padding: 16px;
  border-bottom-right-radius: 16px;
  border-bottom-left-radius: 16px;
  background-color: white;
}

.AssurancePackageContainer[data-selected="true"] {
  --AssurancePackageContainerColor: #1f5b8e; /* $assurance-package-container-color */
  --AssurancePackageRibbonColor: #6bd1ff; /* $ribbon-color */
}

.AssurancePackageContainer[data-selected="true"] > .AssurancePackageFooter {
  position: relative;
  border: 2px solid #1f5b8e;
}

.AssurancePackageContainer[data-selected="true"] > .AssurancePackageFooter::before {
  content: "";
  aspect-ratio: 1/1;
  position: absolute;
  top: calc(100% + 1px);
  left: 50%;
  width: 6%;
  border-bottom: 2.4px solid #1f5b8e;
  border-left: 2.4px solid #1f5b8e;
  transform-origin: center;
  transform: translateX(-50%) translateY(-50%) scaleY(0.6) rotateZ(-45deg);
  background-color: white;
}