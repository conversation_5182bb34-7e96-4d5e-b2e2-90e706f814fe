import { LightningElement, api } from 'lwc';
import { utils } from 'c/utils';
import { utilsPegaText } from 'c/utilsPegaText';

const CarrelloPURendering = {
    Carrello: '<PERSON>ello',
    Footer: 'Footer'
};

export default class <PERSON>ello extends LightningElement {
    _field;
    _groups;
    _carrelloRendering = CarrelloPURendering.Carrello;

    headerUnicaIcon;
    headerRibbonValue;
    renderGroups;

    testoVediDiPiu;
    testoVediDiMeno;
    stileTestoVedi;
    contentFooter;

    statoFooterAperto = true;

    @api
    set field(input) {
        if (input) {
            this._field = input;

            this._checkAndSetup();

        }
    }

    @api
    set groups(input) {
        if (input) {
            this._groups = input;
            this._checkAndSetup();
         }
    }

    _checkAndSetup() {
        if (this._field && this.groups) {
            const tipo = this._field?.customAttributes?.customType;
            if (tipo === 'CarrelloPUFooter') {
                this._setupCarrelloPuFooter();
                this._carrelloRendering = CarrelloPURendering.Footer;
            } else {
                this._setupCarrelloPu();
                this._carrelloRendering = CarrelloPURendering.Carrello;
            }
        }
    }

    get groups() {
        return this._groups;
    }

    get field() {
        return this._field;
    }

    get isCarrello() {
        return this._carrelloRendering === CarrelloPURendering.Carrello;
    }

    get isFooter() {
        return this._carrelloRendering === CarrelloPURendering.Footer;
    }

    get testoVedi() {
        return this.statoFooterAperto ? this.testoVediDiMeno : this.testoVediDiPiu;
    }

    get footerHeaderClass() {
        return `CarrelloPuFooterHeader ${this.statoFooterAperto ? 'statoAperto' : ''}`;
    }

    handleVediDiPiuClick() {
        this.statoFooterAperto = !this.statoFooterAperto;
    }

    _setupCarrelloPu() {
        const headerLayout = utils.getFirstLayoutInGroupsByGroupFormat(this.groups, 'CarrelloPUHeader');
        if (headerLayout) {
            this.headerUnicaIcon = utils.getFirstIconInGroupsByResource(headerLayout.groups, 'unicoLogo');
            if (this.headerUnicaIcon?.customAttributes) {
                this.headerUnicaIcon.customAttributes.webResponsiveSize = '40L 40L 40L';
            }

            const ribbonLayout = utils.getFirstLayoutInGroupsByGroupFormat(this.groups, 'CarrelloPURibbon');
            if (ribbonLayout) {
                this.headerRibbonValue = utils.getFirstCaptionInGroups(ribbonLayout.groups);
                if (this.headerRibbonValue) {
                    this.headerRibbonValue.value = `-${this.headerRibbonValue.value}%`;
                }
            }
        }

        this._filtraRenderGroups(this.groups);
    }

    _filtraRenderGroups(groups) {
        this.renderGroups = groups?.filter(group => {
            const isHidden = group?.field?.control?.type === 'pxHidden';
            const hasHeaderView = group?.view &&
                utils.getFirstLayoutInGroupsByGroupFormat(group.view.groups, 'CarrelloPUHeader');
            const isHeaderLayout = group?.layout?.groupFormat === 'CarrelloPUHeader';
            return !(isHidden || hasHeaderView || isHeaderLayout);
        });
    }

    _setupCarrelloPuFooter() {
        const field = this._field;
        this.testoVediDiMeno = utils.getCustomAttributeFromField(field, 'textShowLess');
        this.testoVediDiPiu = utils.getCustomAttributeFromField(field, 'textShowMore');
        this.stileTestoVedi = utilsPegaText.getTextCss(utils.getCustomAttributeFromField(field, 'textShowStyle'));

        if (this.groups?.[1]?.layout) {
            this.contentFooter = this.groups[1].layout;
        }
    }

}