<template>
  <!-- CARRELLO SECTION -->
  <template if:true={isCarrello}>
    <div class="PuCarrelloContainer">
      <span class="PuCarrelloHeader">
        <span class="UnicaIcon">
          <template if:true={headerUnicaIcon}>
            <c-field field={headerUnicaIcon}></c-field>
          </template>
        </span>
        <template if:true={headerRibbonValue}>
          <span class="Ribbon">
            <c-field field={headerRibbonValue} field-type="caption"></c-field>
          </span>
        </template>
      </span>

      <template if:true={renderGroups}>
        <template for:each={renderGroups} for:index="index" for:item="group">
          <div key={key} class="PuCarrelloGroups">
            <c-groups groups={group}></c-groups>
          </div>
        </template>
      </template>
    </div>
  </template>

  <!-- FOOTER SECTION -->
  <template if:true={isFooter}>
    <div class="CarrelloPuFooterContainer">
      <span
        onclick={handleVediDiPiuClick}
        class={footerHeaderClass}
      >
        <c-custom-text-styles 
                content={testoVedi} 
                text-css={stileTestoVedi}>
        </c-custom-text-styles> 
      </span>

      <template if:true={statoFooterAperto}>
        <div class="CarrelloPuFooterBody">
          <template if:true={contentFooter}>
            <c-layout layout={contentFooter}></c-layout>
          </template>
        </div>
      </template>
    </div>
  </template>
</template>