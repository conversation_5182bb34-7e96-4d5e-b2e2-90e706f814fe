  <template >
    <template if:true={isWarning}>
      <span class="ToastCardContainer">
        <span class="toastType warning"></span>
        <span class="toastContent">
          {toastMessage}
          <span class="xIcon" onclick={onClickHandler} title="<PERSON>udi"></span>
        </span>
      </span>
    </template>

     <template if:true={isAccepted}>
      <!-- <c-feedback-popup 
          visualizza-feedback-popup={isOpen}  
          contenuto-popup={toastMessage}
          onclose={onClickHandler}>
      </c-feedback-popup> -->
    </template> 
  </template>
