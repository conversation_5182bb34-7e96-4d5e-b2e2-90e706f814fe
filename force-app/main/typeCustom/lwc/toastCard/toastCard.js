import { LightningElement, api } from 'lwc';

export default class ToastCard extends LightningElement {
    // Mappa statica per tenere traccia degli ID dei Toast già chiusi
    static _ToastcardIdStatus = new Map();

    _field;
    _toastMessage = '';
    _toastType = 'warning';
    _isOpen = true;
    _toastId;

    @api
    set field(value) {
        //console.log("[TEST]", this.field.customAttributes);
        this._field = value;
        if (this._field && this._field.customAttributes) {
            const ca = this._field.customAttributes;
            this._toastMessage = ca.toastMessage || '';
            this._toastType = ca.toastType || 'warning';
            this._toastId = ca.toastcardId;
        }
        if (this._toastId !== undefined && ToastCard._ToastcardIdStatus.has(this._toastId)) {
            const lastStatus = ToastCard._ToastcardIdStatus.get(this._toastId);
            this._isOpen = !!this._field && !lastStatus;
        } else {
            this._isOpen = this._field.visible;
        }
    }

    get field() {
        return this._field;
    }

    // Handler per la chiusura del Toast (click sulla X)
    onClickHandler() {
        this._isOpen = false;
        if (this._toastId) {
            ToastCard._ToastcardIdStatus.set(this._toastId, true);
        }
    }

    get toastMessage() {
        return this._toastMessage;
    }

    get isOpen() {
        return this._isOpen;
    }

    get isWarning() {
        return this._toastType === 'warning' && this._isOpen;
    }

    get isAccepted() {
        return this._toastType === 'accepted' && this._isOpen;
    }
}