.carousel-container {
  align-self: normal;
  margin-inline: 24px;
}

.carousel-container {
  display: flex;
  justify-content: center; 
  align-items: center; 
  gap: 1rem; 
  overflow-x: auto; 
  padding: 1rem;
  scroll-snap-type: x mandatory;
  padding-left: 64px !important;
  padding-right: 64px !important;

  @media (max-width: 768px) { 
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}

.showArrow {
  padding-left: 64px !important;
  padding-right: 64px !important;

  @media (max-width: 768px) { 
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
} 

.carousel-container .ARCRightArrow,
.carousel-container .ARCLeftArrow {
  aspect-ratio: 1 / 1 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;
  flex-grow: 0 !important;
  color: white !important;
  width: 40px !important;
  background-color: var(--blue-primary) !important; 
  border-radius: 50% !important;
  transform: translateY(-50%) !important;

  @media (max-width: 768px) { 
    display: none !important;
  }
}

.carousel-container .PinZone {
  display: none !important;

  @media (max-width: 768px) { 
    display: flex !important;
  }
}

.carousel-container .CardProtezioneContainer {
  margin-right: 8px;
}

.cards-not-carousel {
  display: flex;
  width: auto;
  gap: 8px;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}
