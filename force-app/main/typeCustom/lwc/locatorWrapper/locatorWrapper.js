import LEAFLET_CSS from '@salesforce/resourceUrl/omnistudio__LeafletCSS';
import LEAFLET_JS from '@salesforce/resourceUrl/omnistudio__LeafletJS';
import BffInterprete from 'c/bffInterprete';
import { loadScript, loadStyle } from 'lightning/platformResourceLoader';
import { LightningElement, api, track } from 'lwc';

export default class LocatorWrapper extends LightningElement {
  @api stepCorrente;
  @track mapMarkers = [];
  @api userLocation;
  @api apiData;

  _bffInterprete = new BffInterprete(this.config);
  buttonFindAgency = {
    validationMessages: '',
    visible: true,
    labelReserveSpace: false,
    readOnly: false,
    control: {
      modes: [
        {
          tooltip: '',
          modeType: 'ignore',
        },
        {
          tooltip: '',
          modeType: 'readOnly',
          autoPrepend: '',
          autoAppend: '',
          controlFormat: 'button seleziona agenzia',
          formatType: 'text',
          showReadOnlyValidation: 'false',
        },
      ],
      actionSets: [
        {
          actions: [
            {
              action: 'runDataTransform',
            },
            {
              action: 'finishAssignment',
            },
          ],
          events: [
            {
              event: 'click',
            },
          ],
        },
      ],
      type: 'pxButton',
      label: 'Seleziona Agenzia',
    },
    label: 'Button',
    type: 'Text',
    required: false,
    validateAs: '',
    reference: 'pyTemplateButton',
    labelFormat: 'Standard',
    disabled: false,
    testID: '202303021648040404700',
    value: '',
    maxLength: 0,
    expectedLength: '',
    fieldID: 'pyTemplateButton',
    customAttributes: {
      'GestioneProcesso.StepSuccessivo': 'MODIFICA TARGA',
    },
    showLabel: false,
  };

  addressAutocompleteTest = {
    label: '',
    control: {
      modes: [{ placeholder: 'Città, Indirizzo, CAP' }],
      options: [],
    },
    readOnly: false,
    labelFormat: 'Text APP GDB16 WEB BAB16 BAB16 BAB16',
    disabled: false,
    value: '',
    reference: '',
  };

  leafletInitialized = false;
  map;

  get isAgencyStep() {
    return this.stepCorrente === 'scelta_agenzia';
  }

  get isInstallerStep() {
    return this.stepCorrente === 'scelta_installatore';
  }

  renderedCallback() {
    if (this.leafletInitialized) return;
    this.leafletInitialized = true;

    if (!this.apiData)
      this.apiData = {
        operationResult: {
          type: '0',
          code: '00',
          message: '',
        },
        poi: [
          {
            delegates: '',
            distance: '0,4',
            closing: '',
            zipCode: '27058',
            phoneNumber: '*********',
            patronalFeast: '',
            divestitureDest: '',
            finitaliaMandate: '0',
            management: "SERVIZI ASSICURATIVI OLTREPO' SRL",
            code: '39680',
            divisionCode: '000000484',
            longitude: '9.010861',
            latitude: '44.991943',
            openingTime:
              '||09:00|13:00|15:00|19:00|09:00|13:00|15:00|19:00|09:00|13:00|15:00|19:00|09:00|13:00|15:00|19:00|09:00|13:00|15:00|19:00|09:00|12:30|CHIUSO|CHIUSO|',
            town: 'VOGHERA',
            suspensionDate: '',
            provinceAcronym: 'PV',
            faxNumber: '*********',
            address: 'VIA GIUSEPPE GARIBALDI 37',
            itemType: 'AGE',
            provinceCode: '018',
            brands: '',
            newManagementCode: '',
            email: '<EMAIL>',
            name: 'VOGHERA',
            treatedBranches: 'X|X|X|X|X|X',
            locationIstatCode: '018182',
            VatTaxCode: '02854400187',
          },
        ],
      };

    if (this.userLocation?.permission === 'granted') {
      Promise.all([loadScript(this, LEAFLET_JS), loadStyle(this, LEAFLET_CSS)])
        .then(() => {
          this.initMap();
        })
        .catch((error) => {
          console.error('Errore caricamento Leaflet:', error);
        });
    }
  }

  popupComponent(marker) {
    let openingTime = marker.openingTime.split('|').filter((time) => time.trim());
    const days = ['Lun', 'Mar', 'Mer', 'Gio', 'Ven', 'Sab', 'Dom'];
    let openingTimeComponent = '';

    for (let i = 0; i < days.length; i++) {
      const offset = i * 4;
      if (openingTime.length <= offset) break;

      const m1 = openingTime[offset];
      const m2 = openingTime[offset + 1];
      const e1 = openingTime[offset + 2];
      const e2 = openingTime[offset + 3];

      const isClosedAllDay = [m1, m2, e1, e2].every((time) => time === 'CHIUSO');
      if (isClosedAllDay) continue;

      const morning = m1 !== 'CHIUSO' && m2 !== 'CHIUSO' ? `${m1} - ${m2}` : null;
      const evening = e1 !== 'CHIUSO' && e2 !== 'CHIUSO' ? `${e1} - ${e2}` : null;

      openingTimeComponent += `<div class="tpd_openingTime">
                                <span class="tdp_openingTime_day">${days[i]}&nbsp; </span>
                                ${morning ? `<span class="tdp_openingTime_morning">${morning}</span>` : ''}
                                ${morning && evening ? `<span>/</span>` : '<span class="tdp_openingTime_evening">FILIALE CHIUSA</span>'}
                                ${evening ? `<span class="tdp_openingTime_evening">${evening}</span>` : ''}
                              </div>`;
    }

    openingTimeComponent += '</div>';

    return `<div class="locator-info-container" onclick="document.querySelector('.leaflet-popup-close-button')?.click()">
              <div class="tpd-icons">
                <img src="/UnipolSaiThemeDynamicWAR/themes/html/dynamicSpots/assets/images/locator/Risorsa4.png" alt="" class="tpd-icon">
              </div>
              <div class="locator-info-data">
                <div class="tpd_agency_name">Agenzia ${marker.code} - Agenzia di ${marker.management} </div>
                <div class="tpd_bold">${marker.distance} km</div>
                <div class="tpd_agency_address tpd_normal">
                  ${marker.address} ${marker.code}, ${marker.zipCode}, ${marker.name}
                </div>
                <div class="tpd_agency_contacts tpd_normal">                
                  <span class="tpd_agency_phoneNumber">Tel:</span> ${marker.phoneNumber}
                  <span class="tpd_agency_faxNumber"> - Fax: </span> ${marker.faxNumber}
                </div>
                <div class="tpd_agency_email tpd_normal">
                  <a href="mailto:${marker.email}" tabindex="0">
                    ${marker.email}
                  </a>
                </div>
                <div class="tpd_openingTime_wrapper">
                  ${openingTimeComponent}
                </div>
                <div class="tpd_cta_agency" style="text-align: center;">
                    <c-field-button class="button-seleziona-agenzia" field={buttonFindAgency}
                        disabled={isDisabled}></c-field-button>
                </div>
                <!--div class="tpd_cta_agency" style="text-align: center;">
                  <button class="button-seleziona-agenzia" onclick={${agencySelector}}>Seleziona Agenzia</button>
                </div-->
              </div>
            </div>`;
  }

  initMap() {
    const mapContainer = this.template.querySelector('.map');
    this.map = L.map(mapContainer).setView(
      [this.userLocation.latitude, this.userLocation.longitude],
      15
    );

    L.tileLayer('https://api.maptiler.com/maps/streets/{z}/{x}/{y}.png?key=JCo8QnUohaRIiSirUQbs', {
      attribution: '© Satellite Provider',
    }).addTo(this.map);

    const customMarker = L.divIcon({
      className: 'custom-html-marker',
      html: `<div role="button" class="map-marker_button">   
              <a>
                <img class="map-marker" draggable="false" />
              </a>           
            </div>`,
      iconSize: [30, 30],
      iconAnchor: [15, 15], // centro del div
    });

    // TODO AGGIUNGERE TEMPLETE:TRUE NEL TEMPLATE PER PASSARE I DATI DELLA CHIAMATA
    this.apiData?.poi.forEach((APIMarker) => {
      if (APIMarker.latitude && APIMarker.longitude) {
        L.marker([APIMarker.latitude, APIMarker.longitude], { icon: customMarker })
          .addTo(this.map)
          .bindPopup(this.popupComponent(APIMarker));

        //.bindPopup(APIMarker.management);
      }
    });

    L.circleMarker([this.userLocation.latitude, this.userLocation.longitude], {
      radius: 4,
      color: 'white', //bordo
      fillColor: '#66cbf1',
      fillOpacity: 1,
    }).addTo(this.map);
  }

  moveToAgency(evt) {
    const { latitude, longitude } = evt.currentTarget.dataset;
    this.map.panTo([latitude, longitude], {
      animate: true,
      duration: 1.0,
    });
  }

  simulateComponentReady() {
    this.dispatchEvent(new CustomEvent('componentready'));
  }

  selectMockAgency() {
    const mock = {
      name: 'Agenzia Mario Rossi',
      code: 'AG001',
      phoneNumber: '0123456789',
      email: '<EMAIL>',
      address: 'Via Roma 1',
      zipCode: '00100',
      town: 'Roma',
      provinceAcronym: 'RM',
    };
    this.dispatchEvent(new CustomEvent('agencyselected', { detail: mock }));
  }

  selectMockInstaller() {
    const mock = {
      code_ext: 'OFF123',
      phoneNumber: '0987654321',
      email: '<EMAIL>',
      address: 'Via Meccanici 10',
      zipCode: '20100',
      town: 'Milano',
      provinceAcronym: 'MI',
    };
    this.dispatchEvent(new CustomEvent('installerselected', { detail: mock }));
  }
}
