.locator-wrapper-mock {
  width: 100%;
  max-height: 623px;
  overflow: auto;
  background-color: var(--ivory);
  position: relative;
}

.map.leaflet-container {
  width: 100%;
  position: relative;
  overflow: hidden;
}

@media (max-width: 767px) {
  .map.leaflet-container {
    height: 450px !important;
  }
}

@media (min-width: 768px) {
  .map.leaflet-container {
    height: 700px !important;
  }
}

.map-marker_button {
  width: 25px;
  height: 30px; 
  overflow: hidden; 
  position: absolute;
  cursor: pointer;
  touch-action: none; 
  left: 3px; 
  top: -4px; 
  z-index: 26;
}

.map-marker {
  width: 25px;
  height: 30px;
  user-select: none;
  border: 0px;
  padding: 0px;
  margin: 0px;
  max-width: none;
  background: url("https://unipolsai.it/UnipolSaiThemeDynamicWAR/themes/html/dynamicSpots/assets/images/locator/Risorsa4.png");
}

.locator-info-container {
  display: flex;
  gap: 16px;
}

.locator-info-data > .tpd_agency_name {
  font-size: 24px;
  line-height: 29px;
  color: #183a56;
  margin-bottom: 2px;
  word-break: break-word !important;
}

.locator-info-data > .tpd_bold {
  font-family: Unipol Bold;
  font-size: 16px;
  line-height: 19px;
  color: #183a56;
  margin-bottom: 12px;
}

.tpd_agency_address {
  font-size: 16px;
  line-height: 19px;
  color: #666;
  margin-bottom: 8px;
  word-break: break-word !important;
}

.tpd_agency_contacts {
  font-size: 16px;
  line-height: 19px;
  color: #666;
  margin-bottom: 8px;
}

.tpd_agency_email {
  color: #666;
  font-size: 16px;
  line-height: 19px;
  margin-bottom: 21px;
}

@media (max-width: 768px) {
  .tpd_agency_email {
    margin-bottom: 5px;
  }
}

.tpd_agency_email > a {
  color: #183a56;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-family: 'Unipol Bold';
}

.tpd_normal {
  font-family: var(--font-family-default);
}

.tpd_openingTime {
  font-family: Unipol !important;
  margin-bottom: 12px !important;
  font-size: 16px !important;
  line-height: 19px !important;
  color: #666 !important;
}

.tdp_openingTime_day {
  font-family: Unipol Medium !important;
  color: #5393bc !important;
}

.button-seleziona-agenzia {
  border-radius: 25px;
  border: none;
  color: #fff;
  text-align: center;
  font-family: Unipol Bold;
  font-size: 16px;
  background-color: #c4151c;
  min-width: 200px;
  min-height: 48px;
}

.tpd_wrapper_container_list_agencies {
  padding-left: 20px;
  /* max-height: 400px !important;
  overflow: scroll; */
  font-family: var(--font-family-default);
  position: absolute;
  top: 77px;
  left: 20px;
  display: block;
}

.tpd_listAgencies {
  background: transparent;
  max-height: 623px;
  overflow: auto !important;
  overflow-x: hidden !important;
  color: #0f3250;
  line-height: 2;
  text-align: left;
  width: 402px;
}

/* @media (max-width: 1280px) { */
@media (max-width: 1025px) {
  .tpd_wrapper_container_list_agencies {
    position: relative;
    left: 0;
    top: 0;
    padding: 20px;
  }

  .tpd_listAgencies {
    width: 100%;
    max-height: 623px;
    overflow: auto;
  }
}


.tpd-item-agency {
  border: 1px solid #c1c1c1;
  margin-bottom: 20px;
  background-color: #fff;
  padding: 20px;
  margin-right: 7px;
}

.locator-wrapper-mock .tpd-icons {
  width: 35px;
  position: relative;
  float: left;
  padding: 40px 0;
}

.locator-wrapper-mock .tpd-agency-name {
  font-size: 24px;
  line-height: 29px;
  padding-bottom: 0;
  padding-left: 30px;
}

.tpd_bold {
  margin-left: 30px
}

.tpd-align-center {
  text-align: center;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.btn.btn-default {
  border-radius: 0;
  width: 153px;
  height: 47px;
  margin-top: 16px;
  margin-bottom: 10px;
  border: 1px solid #979797;
  border-bottom: 5px solid #c4151c;
  font-size: 16px;
  color: #0f3250;
  font-family: var(--font-family-bold);
  background: #fff !important;
}