<template>
    <div class="locator-wrapper-mock">
        <p>Step corrente: {stepCorrente}</p>

        <lightning-button label="Simula caricamento completato" onclick={simulateComponentReady}>
        </lightning-button>

        <template if:true={isAgencyStep}>
            <lightning-button label="Seleziona Agenzia Mock" onclick={selectMockAgency}>
            </lightning-button>
        </template>

        <template if:true={isInstallerStep}>
            <lightning-button label="Seleziona Installatore Mock" onclick={selectMockInstaller}>
            </lightning-button>
        </template>

        <div class="map" lwc:dom="manual"></div>

        <div class="tpd_wrapper_container_list_agencies">
            <c-field-px-auto-complete field={addressAutocompleteTest}></c-field-px-auto-complete>
            <div class="tpd_listAgencies">
                <template for:each={apiData.poi} for:item="marker">
                    <div key={key} class="tpd-item-agency">
                        <div class="tpd-icons">
                            <img src="/UnipolSaiThemeDynamic…l/dynamicSpots/assets/images/locator/Risorsa2.png"
                                alt="" />
                        </div>
                        <div class="tpd-agency-name">AGENZIA {marker.code} - Agenzia di {marker.management}</div>
                        <div class="tpd-agency-wrapper">
                            <div class="tpd-agency-left">
                                <div class="tpd_bold">
                                    <span>{marker.distance}km</span>
                                </div>
                                <div class="tpd_agency_address tpd_normal">
                                    <span>{marker.address} {marker.code}, {marker.zipCode}, {marker.name}</span>
                                </div>
                                <div class="tpd-align-center">
                                    <button class="btn btn-default tpd_agency_btn" data-latitude={marker.latitude}
                                        data-longitude={marker.longitude} onclick={moveToAgency}>Seleziona</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>