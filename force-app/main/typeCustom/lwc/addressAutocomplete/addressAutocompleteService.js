/**
 * Servizio per l'elaborazione dei dati di indirizzo
 */
const addressAutocompleteService = {
  /**
   * Recupera le informazioni dall'oggetto place restituito dall'API Google Maps
   * @param {Object} place - L'oggetto place restituito dall'API
   * @returns {Object} - Le informazioni estratte dall'indirizzo
   */
  recuperaInformazioniDalPlace(place) {
    if (!place || !place.address_components) {
      console.error('Oggetto place non valido o mancante di address_components');
      return {};
    }

    console.log('Recupero informazioni da place:', place);

    const result = {
      nomeStrada: '',
      civico: '',
      comune: '',
      provincia: '',
      cap: '',
      stato: 'Italia',
      codiceCatastaleComune: ''
    };

    const route = place.address_components.find(component =>
      component.types.includes('route'));
    if (route) {
      result.nomeStrada = route.long_name;
    }

    const streetNumber = place.address_components.find(component =>
      component.types.includes('street_number'));
    if (streetNumber) {
      result.civico = streetNumber.long_name;
    }

    const locality = place.address_components.find(component =>
      component.types.includes('locality'));
    if (locality) {
      result.comune = locality.long_name;
    }

    const administrativeArea = place.address_components.find(component =>
      component.types.includes('administrative_area_level_2'));
    if (administrativeArea) {
      result.provincia = administrativeArea.short_name;
    }

    const postalCode = place.address_components.find(component =>
      component.types.includes('postal_code'));
    if (postalCode) {
      result.cap = postalCode.long_name;
    }

    const country = place.address_components.find(component =>
      component.types.includes('country'));
    if (country) {
      result.stato = country.long_name;
    }

    result.codiceCatastaleComune = this.getCodiceComune(result.comune, result.provincia);

    console.log('Informazioni estratte:', JSON.stringify(result));
    return result;
  },

  /**
   * Ottiene il codice catastale del comune
   * @param {string} comune - Il nome del comune
   * @param {string} provincia - La sigla della provincia
   * @returns {string} - Il codice catastale del comune
   */
  getCodiceComune(comune, provincia) {
    if (!comune) return '';
    const codiciComuni = {
      'Roma': 'H501',
      'Milano': 'F205',
      'Napoli': 'F839',
      'Torino': 'L219',
      'Bologna': 'A944',
      'Firenze': 'D612',
      'Genova': 'D969',
      'Bari': 'A662',
      'Catania': 'C351',
      'Venezia': 'L736',
      'Verona': 'L781',
      'Messina': 'F158',
      'Padova': 'G224',
      'Trieste': 'L424',
      'Brescia': 'B157',
      'Parma': 'G337',
      'Taranto': 'L049',
      'Prato': 'G999',
      'Modena': 'F257',
      'Reggio Calabria': 'H224',
      'Reggio Emilia': 'H223',
      'Perugia': 'G478',
      'Livorno': 'E625',
      'Cagliari': 'B354',
      'Foggia': 'D643',
      'Rimini': 'H294',
      'Salerno': 'H703',
      'Ferrara': 'D548',
      'Sassari': 'I452',
      'Latina': 'E472',
      'Giugliano in Campania': 'E054',
      'Monza': 'F704',
      'Siracusa': 'I754',
      'Pescara': 'G482',
      'Bergamo': 'A794',
      'Forlì': 'D704',
      'Vicenza': 'L840',
      'Trento': 'L378',
      'Novara': 'F952',
      'Piacenza': 'G535',
      'Ancona': 'A271',
      'Andria': 'A285',
      'Udine': 'L483',
      'Bolzano': 'A952',
      'Treviso': 'L407',
      'Varese': 'L682'
    };

    const comuneNormalizzato = comune.trim();
    const comuneCapitalizzato = comuneNormalizzato.charAt(0).toUpperCase() + comuneNormalizzato.slice(1).toLowerCase();

    if (codiciComuni[comuneCapitalizzato]) {
      return codiciComuni[comuneCapitalizzato];
    }

    for (const key in codiciComuni) {
      if (comuneCapitalizzato.includes(key) || key.includes(comuneCapitalizzato)) {
        return codiciComuni[key];
      }
    }

    let codiceGenerato = '';
    if (comuneCapitalizzato.length > 0) {
      codiceGenerato += comuneCapitalizzato.charAt(0).toUpperCase();
    } else {
      codiceGenerato += 'X';
    }

    const hashCode = Math.abs(this._stringHashCode(comuneCapitalizzato));
    const hashString = hashCode.toString().padStart(3, '0');
    codiceGenerato += hashString.substring(0, 3);

    if (provincia && provincia.length > 0) {
      codiceGenerato += provincia.substring(0, 1).toUpperCase();
    } else {
      codiceGenerato += 'X';
    }

    return codiceGenerato;
  },

  /**
   * Genera un codice hash da una stringa
   * @private
   * @param {string} str 
   * @returns {number} 
   */
  _stringHashCode(str) {
    let hash = 0;
    if (str.length === 0) return hash;

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }

    return hash;
  },

  /**
   * Formatta i dati dell'indirizzo per l'invio
   * @param {Object} addressInfo 
   * @returns {Object} -
   */
  formattaDatiPerInvio(addressInfo) {
    if (!addressInfo) return {};

    return {
      nomeStrada: addressInfo.nomeStrada || '',
      numeroCivico: addressInfo.civico || '',
      comune: addressInfo.comune || '',
      provincia: addressInfo.provincia || '',
      cap: addressInfo.cap || '',
      stato: addressInfo.stato || 'Italia',
      codiceCatastaleComune: addressInfo.codiceCatastaleComune || this.getCodiceComune(addressInfo.comune, addressInfo.provincia)
    };
  }
};

export { addressAutocompleteService };
