// Chiave API Google Maps - Using the working key from test
const GOOGLE_API_KEY = 'AIzaSyCOyfBmSpLkC7mpvBN0gfu20R-pANS2tMU';

/**
 * Service for loading Google Maps APIs
 */
class MapsService {

  // Static variable to track if API is loaded
  static _ApiLoaded = false;

  constructor() {
    // DO-NOTHING
  }

  /**
   * Load the API
   * @returns {Promise<boolean>} true if loaded successfully
   */
  async loadService() {
    let success = false;

    if (typeof window === 'object') {
      if (!MapsService._ApiLoaded) {
        try {
          // Use API key directly in LWC 
          if (GOOGLE_API_KEY && GOOGLE_API_KEY !== 'INSERISCI_QUI_LA_TUA_CHIAVE_API_REALE') {
            success = await this._loadApi(GOOGLE_API_KEY);
          }

          if (success) {
            MapsService._ApiLoaded = true;
          }
        } catch (exception) {
          console.error('MapsService: Error loading Google service', exception);
        }
      } else {
        success = true;
      }
    }

    return success;
  }

  /**
   * Load the API
   * @param {string} key - API access key
   * @returns {Promise<boolean>} true if loaded successfully
   * @private
   */
  _loadApi(key) {
    return new Promise((resolve) => {
      // Check if Google Maps is already available
      if (window.google?.maps?.places) {
        MapsService._ApiLoaded = true;
        resolve(true);
        return;
      }

      // Check if there's already a script loading
      const existingScript = document.querySelector('script[src*="maps.googleapis.com"]');
      if (existingScript) {
        // Wait for it to load
        const checkInterval = setInterval(() => {
          if (window.google?.maps?.places) {
            clearInterval(checkInterval);
            MapsService._ApiLoaded = true;
            resolve(true);
          }
        }, 100);

        // Timeout after 10 seconds
        setTimeout(() => {
          clearInterval(checkInterval);
          resolve(false);
        }, 10000);

        return;
      }

      // Create and load script
      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${key}&libraries=places`;
      script.async = true;

      script.onload = () => {
        // Wait a moment to ensure everything is initialized
        setTimeout(() => {
          if (window.google?.maps?.places) {
            MapsService._ApiLoaded = true;
            resolve(true);
          } else {
            resolve(false);
          }
        }, 100);
      };

      script.onerror = () => {
        resolve(false);
      };

      document.head.appendChild(script);
    });
  }

  /**
   * Getter to check if API is loaded
   * @returns {boolean} true if API is loaded
   */
  get isApiLoaded() {
    return MapsService._ApiLoaded;
  }
}

export const mapsService = new MapsService();
