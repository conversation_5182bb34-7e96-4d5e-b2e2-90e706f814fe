<template>
  <template if:true={showSubmitBlocker}>
    <c-dx-submit-blocker></c-dx-submit-blocker>
  </template>
  <div class="AddressComponentContainer">
    <template if:false={isManualState}>
      <div class="ContainerInserimentoCivico">
        <div class="InputContainer">
          <c-custom-text-styles content={labelAutocomplete} text-css={stileLabels}></c-custom-text-styles>
          <div class="input-wrapper">
            <input type="text" class={inputPrincipaleClass} placeholder={placeholder} value={inputPrincipaleUtente}
              oninput={handleInputPrincipale} onfocus={handleInputFocus} onblur={handleInputBlur}
              data-id="inputPrincipale" />

            <template if:true={shouldShowDropdown}>
              <div class="dropdown-container">
                <div if:true={isSearchingPredictions} class="loading-indicator">
                  <div class="spinner"></div>
                  <span>Ricerca in corso...</span>
                </div>
                <template if:false={isSearchingPredictions}>
                  <template for:each={predictions} for:item="prediction">
                    <div key={prediction.placeId} class="dropdown-item" data-place-id={prediction.placeId}
                      onclick={handlePredictionSelect}>
                      <div class="prediction-main">{prediction.mainText}</div>
                      <div class="prediction-secondary">{prediction.secondaryText}</div>
                    </div>
                  </template>
                  <div class="powered-by-google">
                    Powered by
                    <img src="https://developers.google.com/static/maps/documentation/images/google_on_white.png"
                      alt="Google" />
                  </div>
                </template>
              </div>
            </template>
          </div>
        </div>

        <template if:true={isIndirizzoState}>
          <div class="InputContainer" style="width: 30%">
            <c-custom-text-styles content="Civico" text-css={stileLabels}></c-custom-text-styles>
            <input type="text" class={inputSecondarioClass} placeholder="es: 21" value={inputSecondarioUtente}
              oninput={handleInputSecondario} data-id="inputSecondario" />
          </div>
        </template>

      </div>

      <template if:true={showButtonConfermaInserimentoCivico} >
        <div onclick={handleConfermaCivico}>
          <c-field-button field={buttonConfermaInserimentoCivico}></c-field-button>
        </div>
      </template>

      <template if:true={showNonTroviIlTuoIndirizzo}>
        <div class="ContainerCambioInserimento" >
          <template if:true={nonTroviIlTuoIndirizzoLabel}>
            <c-custom-text-styles content={nonTroviIlTuoIndirizzoLabel} text-css={stileLabels}></c-custom-text-styles>
          </template>
          <div onclick={handlePassaggioIndirizzoManuale}>
            <c-field-button field={nonTroviIlTuoIndirizzoCta}></c-field-button>
          </div>
        </div>
      </template>

      <template if:true={showErrorMessage}>
        <span class="testo-non-valido">
          <c-field-px-icon class="icon-Attenzione-pieno bd-icona"></c-field-px-icon>
          {errorMessage}
        </span>
      </template>
    </template>

    <template if:true={showInserimentoManuale}>
      <div class="ContainerInserimentoManuale">
        <c-view view={viewInserimentoManuale}></c-view>
        <template if:true={showErrorMessage}>
          <span class="testo-non-valido">
            <c-field-px-icon class="icon-Attenzione-pieno bd-icona"></c-field-px-icon>
            {errorMessage}
          </span>
        </template>
        
        <div if:true={buttonConfermaIndirizzoManuale} onclick={handleConfermaManuale}>
          <c-field-button field={buttonConfermaIndirizzoManuale} ></c-field-button>
        </div>

        <div if:true={buttonAnnullaInserimentoManuale} onclick={handlePassaggioAutocompletamento}>
          <c-field-button field={buttonAnnullaInserimentoManuale} ></c-field-button>
        </div>

      </div>
    </template>
  </div>
</template>
