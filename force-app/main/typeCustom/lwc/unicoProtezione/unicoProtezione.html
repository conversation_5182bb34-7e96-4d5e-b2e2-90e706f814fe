<template>
  <div class={containerClass}>
    <template if:true={renderGroup}>
      <template for:each={renderGroup} for:index="index" for:item="group">
        <div key={key}>
          <c-groups groups={group}></c-groups>
        </div>
      </template>
    </template>

    <template if:true={hasConvenzione}>
      <span class="UnicoPortezioneRibbon">
        <c-custom-text-styles content={testoConvenzione} text-css={stileTestoConvenzione}></c-custom-text-styles>
        <c-custom-text-styles content={convenzione} text-css={stileConvenzione}></c-custom-text-styles>
      </span>
    </template>
  </div>
</template>