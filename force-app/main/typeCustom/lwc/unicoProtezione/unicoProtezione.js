import { LightningElement, api } from 'lwc';
import { utils } from 'c/utils';
import { utilsPegaText } from 'c/utilsPegaText';

export default class UnicoProtezione extends LightningElement {
    _field;
    _groups;
    _initialized = false;

    productType;
    renderGroup;

    stileTestoConvenzione;
    stileConvenzione;
    testoConvenzione;
    convenzione;

    @api
    set field(value) {
        this._field = value;
        this._tryInit();
    }

    get field() {
        return this._field;
    }

    @api
    set groups(value) {
        this._groups = value;
        this._tryInit();
    }

    get groups() {
        return this._groups;
    }

    get hasConvenzione() {
        return this.testoConvenzione && this.convenzione;
    }

    get containerClass() {
        return `UnicoPortezioneContainer unicoProtezioneType${this.productType || ''}`;
    }

    renderedCallback() {
        this._tryInit(); // fallback, nel caso i dati arrivino *dopo* il render
    }

    _tryInit() {
        if (this._initialized || !this._field || !this._groups) return;

        this._initialized = true;
        this.productType = this._field?.customAttributes?.productType;

        const unicoProtezioneRibbon = utils.getFirstLayoutInGroupsByGroupFormat(this._groups, 'UnicoProtezioneRibbon');

        if (unicoProtezioneRibbon) {
            this.stileTestoConvenzione = utilsPegaText.getTextCss('TEXT APP BDB16 WEB WHL14 WHL14 WHL14');
            this.stileConvenzione = utilsPegaText.getTextCss('TEXT APP BDB16 WEB WHB14 WHB14 WHB14 WHB14');

            const captions = utils.getEachCaptionInGroups(unicoProtezioneRibbon.groups);
            if (captions?.length === 2) {
                this.testoConvenzione = captions[0].value;
                this.convenzione = captions[1].value;
            }
        }

        this.renderGroup = this._groups.filter(group => {
            const isHidden = group?.field?.control?.type === 'pxHidden';
            const isRibbonLayout = group?.layout?.groupFormat === 'UnicoProtezioneRibbon';
            return !(isHidden || isRibbonLayout);
        });
    }
}