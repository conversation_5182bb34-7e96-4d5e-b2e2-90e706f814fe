import { LightningElement, api, track } from 'lwc';

export default class MultiStepper extends LightningElement {
    _field;
    @track steps = [];
    actualStep = 0;
    numberOfSteps = 0;

    @api
    get field() {
        return this._field;
    }
    set field(value) {
        this._field = value;
        const ca = value.customAttributes || {};
        // Converte le stringhe in numero (default: step corrente 0, almeno 1 step)
        this.actualStep = parseInt(ca.actualStep, 10) || 0;
        this.numberOfSteps = parseInt(ca.numberOfSteps, 10) || 1;
        this.generateSteps();
    }

    generateSteps() {
        this.steps = [];
        for (let i = 1; i <= this.numberOfSteps; i++) {
            this.steps.push({
                id: `step-${i}`,
                stepClass: i <= this.actualStep ? 'Step selectedStep' : 'Step'
            });
        }
    }

    connectedCallback() {
        console.log("[DEBUG] componente renderezzita: ", this._field);
    }
    
}