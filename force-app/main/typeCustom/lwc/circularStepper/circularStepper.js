import { LightningElement, api } from 'lwc';

export default class CircularStepper extends LightningElement {
    _field;
    totalStep = 0;
    currentStep = 0;

    // Il setter legge il campo in ingresso e converte i customAttributes
    @api
    set field(input) {
        if (input) {
            this._field = input;
            const ca = input.customAttributes || {};
            // Converte i valori da stringa a numero (default 0 per totalStep e 0 per currentStep)
            this.totalStep = Number(ca.TotalStep) || 0;
            this.currentStep = Number(ca.CurrentStep) || 0;
        }
    }

    get field() {
        return this._field;
    }

    // Getter che calcola lo stile inline, sostituendo la variabile CSS --statusProgress
    get progressStyle() {
        let degrees = 0;
        if (this.totalStep > 0) {
            degrees = (this.currentStep / this.totalStep) * 360;
        }
        return `--statusProgress: ${degrees}deg;`;
    }
}