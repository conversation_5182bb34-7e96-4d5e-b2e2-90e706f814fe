.CardSezioneContainer {
  width: 100%;
  max-width: 1062px;
  margin: 0 auto 32px;
  border: 1px solid #1f5b8e;
  border-left-width: 8px;
  border-radius: 24px;
  padding: 24px;
}

.CardSezioneContainer .Cancel-button {
  margin: 0 !important;
  max-width: 100%;
}

@media (max-width: 768px) {
  .CardSezioneContainer field:has(.separator) {
    display: none;
  }

  .CardSezioneContainer .Cancel-button {
    margin-top: 24px !important;
  }
}

.CardSezioneContainer.deselected {
  position: relative !important;
  border: 0 !important;
  background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='24' ry='24' stroke='%235393BCFF' stroke-width='4' stroke-dasharray='20' stroke-dashoffset='0' stroke-linecap='round'/%3e%3c/svg%3e");
}

.CardSezioneContainer.deselected .deselectedText {
  display: block;
  position: absolute;
  background-color: white;
  top: 0;
  left: 50%;
  padding: 0 16px;
  transform: translateX(-50%) translateY(-50%);
  white-space: nowrap !important;
}