<template>
      <div class={containerClass}>
      
        <c-custom-text-styles
          class="deselectedText"
          text-css={deselectedTextStyle}
          content={deselectedText}>
        </c-custom-text-styles>
        
        <template if:true={renderGroups}>
          <template for:each={renderGroups} for:index="index" for:item="group">
            <div key={key}>
              <c-groups groups={group} key={key}></c-groups>
            </div>
          </template>
        </template>

    </div>
  </template>