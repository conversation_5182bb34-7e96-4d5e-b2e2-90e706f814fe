import { utils } from 'c/utils';
import { LightningElement, api, track } from 'lwc';

export default class Stepper extends LightningElement {
  _field;
  @track progress = '0'; // percentage value (string)
  @track currentStep = 0; // current step (number)
  @track steps = []; // array of pre-calculated steps
  @track labelFormatDeselected = '';
  @track labelFormatPreviousStep = '';
  @track labelFormatCurrentStep = '';
  @track labelsStepper = '';
  @track styleCustom = '';

  // We choose to use version 2 (the complete stepper) by default
  stepperVersion = 2;

  @api
  set field(value) {
    this._field = value;
    if (value && value.customAttributes) {
      this.progress = value.customAttributes.progress || '0';
      this.currentStep = parseInt(value.customAttributes.currentStep || '0', 10);
      this.labelFormatDeselected = value.customAttributes.labelFormatDeselected || '';
      this.labelFormatPreviousStep = value.customAttributes.labelFormatPreviousStep || '';
      this.labelFormatCurrentStep = value.customAttributes.labelFormatCurrentStep || '';
      this.labelsStepper = value.customAttributes.labelsStepper || '';
      this.styleCustom = value.customAttributes.style || '';

      // Constructing the steps array based on labelsStepper
      if (this.labelsStepper) {
        const trimmedSteps = this.labelsStepper.split('|').map((s) => s.trim());
        this.steps = [];
        // For N labels we create an array with (N * 2 - 1) elements (alternating the step and a space)
        for (let i = 0; i < trimmedSteps.length * 2 - 1; i++) {
          if (i % 2 === 0) {
            const stepNumber = Math.floor(i / 2) + 1;
            //A step is considered completed (checked) if stepNumber is less than or equal to currentStep
            const checked = stepNumber <= this.currentStep;
            // displayNumber is pre-calculated: if the step is completed it is left blank, otherwise it shows the number
            const displayNumber = checked ? '' : stepNumber;
            // You may also decide to use a specific label format based on the status:
            let labelFormat = '';
            if (stepNumber === this.currentStep) {
              labelFormat = this.labelFormatCurrentStep;
            } else if (stepNumber < this.currentStep) {
              labelFormat = this.labelFormatPreviousStep;
            } else {
              labelFormat = this.labelFormatDeselected;
            }
            const checkedClass = 'StepperNumber' + (checked ? ' checked' : '');

            // Calcolo delle classi per il nome degli step in base allo stato
            let stepNameClass = 'StepName';
            if (stepNumber === this.currentStep) {
              // Per lo step corrente usa il custom attribute relativo
              stepNameClass += ' ' + utils.getClassFromFormat(this.labelFormatCurrentStep);
            } else if (stepNumber < this.currentStep) {
              stepNameClass += ' ' + utils.getClassFromFormat(this.labelFormatPreviousStep);
            } else {
              stepNameClass += ' ' + utils.getClassFromFormat(this.labelFormatDeselected);
            }

            this.steps.push({
              id: `step-${stepNumber}`, // unique key
              label: trimmedSteps[Math.floor(i / 2)],
              stepNumber,
              checked,
              displayNumber,
              labelFormat,
              checkedClass,
              stepNameClass,
            });
          } else {
            // Placeholder object for "space"
            this.steps.push({
              id: `space-${i}`,
              space: true,
            });
          }
        }
      }
    }
  }

  get field() {
    return this._field;
  }

  get format() {
    return this.field?.customAttributes?.labelFormat
      ? utils.getClassFromFormat(this.field.customAttributes.labelFormat)
      : '';
  }

  get controlFormat() {
    return this.field.control.format;
  }

  // Getter to use for the progress bar (version 1) if needed
  get progressStyle() {
    return `width: ${this.progress}%`;
  }

  // Getter to determine which version to show
  get isProgressVersion() {
    return this.stepperVersion === 1;
  }

  get isStepperVersion() {
    return this.stepperVersion === 2;
  }
}
