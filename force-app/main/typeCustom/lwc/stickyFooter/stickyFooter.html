<template>
  <div class="footer-pu-container">
    <div class="d-flex align-center">

      <template if:true={hasFooterGroups}>
        <div class="containerFooter">
          <template for:each={footerLayout.groups} for:index="index" for:item="group">
            <div key={key}>
              <c-groups groups={group}></c-groups>
            </div>
          </template> 
        </div>     
      <div class="separator visible-tablet"></div>
      </template>

      <template if:true={secondaryButtonVisible}>
        <c-field 
          field={footerSecondaryButton}
          class="visible-tablet">
        </c-field>
      </template>

      <c-field field={footerPositiveButton}></c-field>
    </div>

    <template if:true={secondaryButtonVisible}>
      <c-field 
        field={footerSecondaryButton}
        class="visible-mobile">
      </c-field>
    </template>
  </div>

  <template if:true={showPlaceholder}>
    <div class="footer-pu-placehoder"></div>
  </template>
</template>