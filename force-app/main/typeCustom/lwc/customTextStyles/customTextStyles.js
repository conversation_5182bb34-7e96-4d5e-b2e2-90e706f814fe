import { LightningElement, api } from 'lwc';
import { utilsPegaText } from 'c/utilsPegaText';
import { utils } from "c/utils";

export default class CustomTextStyle extends LightningElement {
    @api content = '';
    @api textCss;

    get labelCssFormat() {
        if (this.textCss) {
          const inlineStyle = utilsPegaText.getTextCss(this.textCss);
    
          if (!inlineStyle) return {};
          return {
            desktopCss: utils.getStyleStringFromObj(inlineStyle.desktopCss),
            tabletCss: utils.getStyleStringFromObj(inlineStyle.tabletCss),
            mobileCss: utils.getStyleStringFromObj(inlineStyle.mobileCss)
          };
        }
    }
}