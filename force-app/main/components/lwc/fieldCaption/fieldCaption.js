import { utils } from 'c/utils';

import { LightningElement, api } from 'lwc';

export default class FieldCaption extends LightningElement {
  _field;

  @api
  decodedValue;

  @api debug;

  @api
  get field() {
    return this._field;
  }

  set field(value) {
    this._field = value;
    this.handleFieldChange();
  }

  get format() {
    if (this.field?.control?.format) {
      return utils.getClassFromFormat(this.field.control.format);
    }
  }

  get componentClass() {
    return `caption ${this.debug ? 'debug' : ''}`.trim();
  }

  handleFieldChange() {
    // console.log("[TEST] field", utils.printObject(this.field));
  }
}
