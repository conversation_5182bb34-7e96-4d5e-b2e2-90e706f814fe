<template>
  <template if:true={field}>
    <template if:true={isVisible}>
      <div class={componentClass}>
        <p if:true={debug} class="temporaryLabel">FIELD: <b>{controlTypeForDebugging}</b></p>

        <template if:true={isPxHidden}>
          <c-field-px-hidden
            debug={debug}
            field={field}
            decoded-value={decodedValue}
            disabled={isDisabled}
            groups={groups}
          ></c-field-px-hidden>
        </template>

        <template if:true={isCaption}>
          <c-field-caption debug={debug} field={field} decoded-value={decodedValue}></c-field-caption>
        </template>

        <template if:true={isParagraph}>
          <c-field-paragraph debug={debug} field={field}></c-field-paragraph>
        </template>

        <template if:true={isLink}>
          <c-field-link
            debug={debug}
            field={field}
            decoded-value={decodedValue}
            disabled={isDisabled}
          ></c-field-link>
        </template>

        <template if:true={isButton}>
          <c-field-button
            debug={debug}
            field={field}
            decoded-value={decodedValue}
            disabled={isDisabled}
          ></c-field-button>
        </template>

        <template if:true={isPxInteger}>
          <c-field-px-integer
            debug={debug}
            field={field}
            decoded-value={decodedValue}
            disabled={isDisabled}
          ></c-field-px-integer>
        </template>

        <template if:true={isPxTextInput}>
          <c-field-input-text
            debug={debug}
            field={field}
            decoded-value={decodedValue}
            disabled={isDisabled}
          ></c-field-input-text>
        </template>

        <template if:true={isPxIcon}>
          <c-field-px-icon debug={debug} field={field}></c-field-px-icon>
        </template>

        <template if:true={isPxRadioButtons}>
          <c-field-px-radio-buttons
            debug={debug}
            field={field}
            decoded-value={decodedValue}
            disabled={isDisabled}
          ></c-field-px-radio-buttons>
        </template>

        <template if:true={isCheckbox}>
          <c-field-checkbox
            debug={debug}
            field={field}
            decoded-value={decodedValue}
            disabled={isDisabled}
          ></c-field-checkbox>
        </template>

        <template if:true={isDateTime}>
          <c-field-date-time
            debug={debug}
            field={field}
            decoded-value={decodedValue}
            disabled={isDisabled}
          ></c-field-date-time>
        </template>

        <template if:true={isPxCurrency}>
          <c-field-px-currency debug={debug} field={field} decoded-value={decodedValue}
            disabled={isDisabled} >
          </c-field-px-currency>
        </template>

        <template if:true={isAutoComplete}>
          <c-field-px-auto-complete
            debug={debug} field={field} decoded-value={decodedValue} disabled={isDisabled}>
          </c-field-px-auto-complete>
        </template>

        <template if:true={isDropdown}>
          <c-field-dropdown
            debug={debug}
            field={field}
            disabled={isDisabled}
            decoded-value={decodedValue}
          ></c-field-dropdown>
        </template>
        
      </div>
    </template>
  </template>
</template>
