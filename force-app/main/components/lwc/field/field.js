import { utils } from 'c/utils';
import { utilsPega } from 'c/utilsPega';
import { LightningElement, api } from 'lwc';

/// <reference path="../utilsPega/utilsPega.jsdoc.js" />

export default class Field extends LightningElement {
  _field;

  /** @type {FieldTypes} */
  _fieldType;

  @api debug;
  @api parentLayout;
  @api groups;

  @api
  get field() {
    return this._field;
  }

  set field(value) {
    this._field = value;
    this.handleFieldChange();
  }

  /** @returns {FieldTypes} */
  @api
  get fieldType() {
    return this._fieldType;
  }

  /** @param {FieldTypes} value */
  set fieldType(value) {
    this._fieldType = value;
  }

  get decodedValue() {
    return this.field?.value ? utils.decodeHTML(this.field.value) : this.field?.value;
  }

  get isVisible() {
    return !!this.field && this.field.visible;
  }

  get isDisabled() {
    if (this.isCheckbox || this.isRadioButtons || this.isPxCurrency) {
      return this.field.readOnly || this.field.disabled;
    }
    return this.field.disabled === true;
  }

  ///////// FIELD TYPES
  get isPxHidden() {
    return this.fieldType === utilsPega.fields.FIELD_TYPES.pxHidden;
  }

  get isCaption() {
    return this.fieldType === utilsPega.fields.FIELD_TYPES.caption;
  }

  get isParagraph() {
    return this.fieldType === utilsPega.fields.FIELD_TYPES.paragraph;
  }

  get isLink() {
    return this.fieldType === utilsPega.fields.FIELD_TYPES.link;
  }

  get isButton() {
    return this.fieldType === utilsPega.fields.FIELD_TYPES.pxButton;
  }

  get isPxInteger() {
    return this.fieldType === utilsPega.fields.FIELD_TYPES.pxInteger;
  }

  get isPxTextInput() {
    return this.fieldType === utilsPega.fields.FIELD_TYPES.pxTextInput;
  }

  get isPxIcon() {
    return this.fieldType === utilsPega.fields.FIELD_TYPES.pxIcon;
  }

  get isCheckbox() {
    return this.fieldType === utilsPega.fields.FIELD_TYPES.pxCheckbox;
  }

  get isPxRadioButtons() {
    return this.fieldType === utilsPega.fields.FIELD_TYPES.pxRadioButtons;
  }

  get isDateTime() {
    return this.fieldType === utilsPega.fields.FIELD_TYPES.pxDateTime;
  }

  get isPxCurrency() {
    return this.fieldType === utilsPega.fields.FIELD_TYPES.pxCurrency;
  }

  get isDropdown() {
    return this.fieldType === utilsPega.fields.FIELD_TYPES.pxDropdown;
  }

  get isAutoComplete() {
    return this.fieldType === utilsPega.fields.FIELD_TYPES.pxAutoComplete;
  }

  handleFieldChange() {
    // console.log("[TEST] field", utils.printObject(this.field));
  }

  // on lighting component initialization
  connectedCallback() {
    if (!this._fieldType) {
      this._fieldType = this.field?.control?.type;
    }
  }

  get controlTypeForDebugging() {
    return this.field.control?.type ?? this._fieldType ?? 'MISSING TYPE';
  }

  get componentClass() {
    return `field ${this.debug ? 'debug' : ''}`.trim();
  }
}
