.groups {
  padding: 0;
  margin: 0;
  width: 100%;
}

.debug {
  border: 1px solid #45da4a;
  padding: 10px;
  margin: 5px 0;
}

.row-layout {
  display: flex;
  flex-direction: row;
}

@media screen and (max-width: 480px) {
  .row-layout {
    align-items: var(--align-items-m);
  }
}
@media screen and (max-width: 768px) {
  .row-layout {
    align-items: var(--align-items-t);
  }
}
@media screen and (min-width: 768px) {
  .row-layout {
    align-items: var(--align-items-d);
  }
}

.GroupDetector {
  /* Stile di base per GroupDetector */
  display: grid;
}

@media screen and (max-width: 480px) {
  .GroupDetector .growMobile,
  .GroupDetector *[class*="growMobile"] {
    flex-grow: 1;
  }
}

.row-layout, .col-layout {
  display: flex;
  --gap-mobile: 0;
  --gap-tablet: 0;
  --gap-desktop: 0;
  --justify-content-m: "center";
  --align-items-m: "center";
  --justify-content-t: "center";
  --align-items-t: "center";
  --justify-content-d: "center";
  --align-items-d: "center";
  --align-content-d: "initial";
  --align-content-t: "initial";
  --align-content-m: "initial";
  --grid-template-columns-d: "none";
  --grid-template-columns-t: "none";
  --grid-template-columns-m: "none";
}

@media screen and (max-width: 480px) {
  .row-layout, .col-layout {
    gap: var(--gap-mobile);
    justify-content: var(--justify-content-m);
    align-content: var(--align-content-m);
    grid-template-columns: var(--grid-template-columns-m);
    align-items: var(--align-items-m);
  }
}

@media screen and (max-width: 768px) {
  .row-layout, .col-layout {
    gap: var(--gap-tablet);
    justify-content: var(--justify-content-t);
    align-content: var(--align-content-t);
    grid-template-columns: var(--grid-template-columns-t);
    align-items: var(--align-items-t);
  }
}

@media screen and (min-width: 768px) {
  .row-layout, .col-layout {
    gap: var(--gap-desktop);
    justify-content: var(--justify-content-d);
    align-content: var(--align-content-d);
    grid-template-columns: var(--grid-template-columns-d);
    align-items: var(--align-items-d);
  }
}