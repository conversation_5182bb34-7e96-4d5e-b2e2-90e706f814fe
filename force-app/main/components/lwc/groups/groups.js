import { utilsPega } from 'c/utilsPega';
import { LightningElement, api } from 'lwc';

export default class Groups extends LightningElement {
  _groups;

  @api debug;
  @api groupClass;
  @api customComponent;
  @api groupFormat;

  @api computedStyles;

  @api
  get groups() {
    return this._groups;
  }

  set groups(value) {
    this._groups = value;
  }

  get isVisible() {
    return !!this.groups && !(this.groups.visible === false);
  }

  get componentClass() {
    return `groups ${this.groupFormatClass || ''} ${this.debug ? 'debug' : ''}`.trim();
  }

  get groupFormatClass() {
    return utilsPega.layout.SUPPORTED_GROUP_FORMATS[this.groupFormat] || '';
  }

  get isPxHidden() {
    return this.groups.field?.control.type == "pxHidden";
  }

}
