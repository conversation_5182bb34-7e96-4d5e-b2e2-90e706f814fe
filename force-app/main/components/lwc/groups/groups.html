<template>
  <template if:true={groups}>
    <template if:true={isVisible}>
      <div class="groupDetector">
      <div if:false={customComponent} class={componentClass} style={computedStyles}>
        <template if:true={groups.view}>
          <template if:true={groups.view.visible}>
            <c-view view={groups.view} debug={debug}></c-view>
          </template>
        </template>

        <template if:true={groups.layout}>
          <c-layout debug={debug} layout={groups.layout}></c-layout>
        </template>

        <template if:true={groups.field}>
          <c-field field={groups.field} debug={debug}></c-field>
        </template>

        <template if:true={groups.caption}>
          <c-field debug={debug} field={groups.caption} field-type="caption"></c-field>
        </template>

        <template if:true={groups.paragraph}>
          <c-field debug={debug} field={groups.paragraph} field-type="paragraph"></c-field>
        </template>

      </div>
      <div if:true={customComponent} class={componentClass}>
        <template if:true={groups.field}>
          <c-field if:true={isPxHidden} field={groups.field} groups={customComponent}  debug={debug}></c-field>
        </template>
      </div>
    </div>
    </template>
  </template>
</template>