/**
 * Servizio per la normalizzazione degli indirizzi
 */
public with sharing class IndirizziService {
    
    // Endpoint del servizio di normalizzazione (da configurare)
    private static final String ENDPOINT_NORMALIZZAZIONE = 'https://unipolsai.it/api/pub/indirizzi/v2/normalizza';
    
    /**
     * Normalizza un indirizzo utilizzando un servizio esterno
     * @param richiesta JSON contenente i dati dell'indirizzo da normalizzare
     * @return JSON contenente l'indirizzo normalizzato e il codice catastale
     */
    @AuraEnabled
    public static String normalizzaIndirizzo(String richiesta) {
        System.debug('Richiesta normalizzazione indirizzo: ' + richiesta);
        
        try {
            // Parsing della richiesta JSON
            Map<String, Object> richiestaMap = (Map<String, Object>)JSON.deserializeUntyped(richiesta);
            
            // Estrai i dati dalla richiesta
            String indirizzo = (String)richiestaMap.get('indirizzo');
            String comune = (String)richiestaMap.get('comune');
            String cap = (String)richiestaMap.get('cap');
            String provincia = (String)richiestaMap.get('provincia');
            
            // Inizializza la risposta
            Map<String, Object> risposta = new Map<String, Object>();
            Map<String, Object> esito = new Map<String, Object>();
            Map<String, Object> localita = new Map<String, Object>();
            
            // Verifica che i dati essenziali siano presenti
            if (String.isBlank(indirizzo) || String.isBlank(comune)) {
                esito.put('stato', 'KO');
                esito.put('messaggio', 'Indirizzo o comune mancanti');
                risposta.put('esito', esito);
                return JSON.serialize(risposta);
            }
            
            // Opzione 1: Chiamata a un servizio esterno
            // String responseBody = chiamaServizioEsterno(indirizzo, comune, cap, provincia);
            // return responseBody;
            
            // Opzione 2: Logica interna di normalizzazione (per test/sviluppo)
            String codiceCatastale = trovaCodiceCatastale(comune, provincia);
            
            // Imposta l'esito positivo
            esito.put('stato', 'OK');
            risposta.put('esito', esito);
            
            // Imposta i dati della località
            localita.put('codiceBelfiore', codiceCatastale);
            risposta.put('localita', localita);
            
            System.debug('Risposta normalizzazione: ' + risposta);
            return JSON.serialize(risposta);
            
        } catch (Exception e) {
            // Gestione degli errori
            System.debug(LoggingLevel.ERROR, 'Errore durante la normalizzazione: ' + e.getMessage());
            
            Map<String, Object> risposta = new Map<String, Object>();
            Map<String, Object> esito = new Map<String, Object>();
            
            esito.put('stato', 'KO');
            esito.put('messaggio', 'Errore durante la normalizzazione: ' + e.getMessage());
            risposta.put('esito', esito);
            
            return JSON.serialize(risposta);
        }
    }
    
    /**
     * Chiama un servizio esterno per normalizzare l'indirizzo
     * @param indirizzo Indirizzo da normalizzare
     * @param comune Comune dell'indirizzo
     * @param cap CAP dell'indirizzo
     * @param provincia Provincia dell'indirizzo
     * @return Risposta del servizio esterno
     */
    private static String chiamaServizioEsterno(String indirizzo, String comune, String cap, String provincia) {
        // Prepara i parametri della richiesta
        Map<String, Object> requestBody = new Map<String, Object>();
        requestBody.put('indirizzo', indirizzo);
        requestBody.put('comune', comune);
        requestBody.put('cap', cap);
        requestBody.put('provincia', provincia);
        
        // Prepara la richiesta HTTP
        HttpRequest req = new HttpRequest();
        req.setEndpoint(ENDPOINT_NORMALIZZAZIONE);
        req.setMethod('POST');
        req.setHeader('Content-Type', 'application/json');
        req.setBody(JSON.serialize(requestBody));
        
        // Esegui la richiesta HTTP
        Http http = new Http();
        HttpResponse res = http.send(req);
        
        // Verifica la risposta
        if (res.getStatusCode() == 200) {
            return res.getBody();
        } else {
            throw new CalloutException('Errore nella chiamata al servizio esterno: ' + res.getStatusCode() + ' ' + res.getStatus());
        }
    }
    
    /**
     * Trova il codice catastale (codice Belfiore) di un comune
     * @param comune Nome del comune
     * @param provincia Provincia del comune
     * @return Codice catastale del comune
     */
    private static String trovaCodiceCatastale(String comune, String provincia) {
        // In una implementazione reale, dovresti:
        // 1. Cercare il codice catastale in un oggetto personalizzato o in una tabella esterna
        // 2. Oppure chiamare un servizio esterno per ottenere il codice
        
        // Per ora, implementiamo una logica semplificata con alcuni comuni comuni
        Map<String, String> codiciComuni = new Map<String, String>{
            'milano' => 'F205',
            'roma' => 'H501',
            'napoli' => 'F839',
            'torino' => 'L219',
            'bologna' => 'A944',
            'firenze' => 'D612',
            'genova' => 'D969',
            'palermo' => 'G273',
            'bari' => 'A662',
            'catania' => 'C351'
        };
        
        // Normalizza il nome del comune (minuscolo e senza spazi)
        String comuneNormalizzato = comune.toLowerCase().trim();
        
        // Cerca il codice catastale
        if (codiciComuni.containsKey(comuneNormalizzato)) {
            return codiciComuni.get(comuneNormalizzato);
        }
        
        // Se non troviamo una corrispondenza esatta, proviamo a cercare una corrispondenza parziale
        for (String key : codiciComuni.keySet()) {
            if (comuneNormalizzato.contains(key) || key.contains(comuneNormalizzato)) {
                return codiciComuni.get(key);
            }
        }
        
        // Se non troviamo nulla, generiamo un codice fittizio basato sul nome del comune
        // Questo è solo per scopi dimostrativi, in produzione dovresti usare dati reali
        String codiceGenerato = 'X' + Math.abs(comune.hashCode()).toString().substring(0, 3);
        return codiceGenerato;
    }
}
