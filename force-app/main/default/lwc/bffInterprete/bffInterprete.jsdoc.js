/** @typedef {0 | 1 | 2} PegaAPI */
/** @typedef {Object<string, PegaAPI>} PEGA_API */

/** @typedef {"CREATE" | "retrieve" | "duplicate" | "reload"} IActionLoadRequest */
/** @typedef {Object<string, IActionLoadRequest>} IActionLoadRequestEnum */

/**
 * @typedef {Object} ILoadPageRequest
 * @property {IActionLoadRequest} action
 * @property {string} [assignmentId] - Required only if action == retrieve ||
 *   reload
 * @property {string} [caseId] - Required only if action == duplicate
 * @property {string} [productType]
 * @property {any} [referencesToUpdate]
 * @property {string} [env]
 */

// /**
//  * @typedef {Object} ICommandResponse
//  * @property {IMetaBodyResponse} metaBodyResponse
//  * @property {IActionResponse} pegaBodyResponse
//  * @property {any} [analyticsBodyResponse]
//  */

// /**
//  * @typedef {Object} IMetaBodyResponse
//  * @property {string} actionId
//  * @property {string} assignmentId
//  */

/**
 * @typedef {Object} IGoNextPageRequest
 * @property {string} [actionId] - If null retrieves first action from
 *   assignment
 * @property {string} assignmentId
 * @property {string} [captchaToken]
 * @property {any} [referencesToUpdate]
 */

/**
 * @typedef {Object} IUpdatePageRequest
 * @property {string} [actionId] - If null retrieves first action from
 *   assignment
 * @property {string} assignmentId
 * @property {any} [referencesToUpdate]
 * @property {string} [refreshFor]
 */

// /**
//  * @typedef {Object} IAssignmentsResponse
//  * @property {string} pxObjClass
//  * @property {IAssignment[]} assignments
//  */

// /**
//  * @typedef {Object} IAssignment
//  * @property {string} caseID
//  * @property {string} executedDeadlineTime
//  * @property {string} executedGoalTime
//  * @property {string} ID
//  * @property {string} name
//  * @property {string} pxObjClass
//  * @property {string} routedTo
//  * @property {string} scheduledDeadlineTime
//  * @property {string} scheduledGoalTime
//  * @property {string} type
//  * @property {number} urgency
//  * @property {string} instructions
//  */

// /**
//  * @typedef {Object} IErrorResponse
//  * @property {string} pxObjClass
//  * @property {IError[]} errors
//  */

// /**
//  * @typedef {Object} IError
//  * @property {string} ID
//  * @property {string} message
//  * @property {string} pxObjClass
//  */

// /**
//  * @typedef {Object} IAssignmentResponse
//  * @property {string} ID
//  * @property {string} caseID
//  * @property {string} name
//  * @property {string} pxObjClass
//  * @property {string} type
//  * @property {string} routedTo
//  * @property {string} instructions
//  * @property {string} scheduledGoalTime
//  * @property {string} executedGoalTime
//  * @property {string} scheduledDeadlineTime
//  * @property {string} executedDeadlineTime
//  * @property {number} urgency
//  * @property {IAssignmentAction[]} actions
//  */

// /**
//  * @typedef {Object} IAssignmentAction
//  * @property {string} ID
//  * @property {string} name
//  * @property {string} type
//  * @property {string} pxObjClass
//  */

// /**
//  * @typedef {Object} IAssignmentPostResponse
//  * @property {string} nextAssignmentID
//  * @property {string} nextPageID
//  */

/**
 * @typedef {Object} IActionResponse
 * @property {string} actionID
 * @property {string} caseID
 * @property {string} name
 * @property {IView} view
 */

/**
 * @typedef {Object} IView
 * @property {string} [reference]
 * @property {string} [validationMessages] - This may be returned by the
 *   '.../refresh' APIs, if the user's edits caused a non-field-specific error
 * @property {string} [viewID]
 * @property {string} [name]
 * @property {string} [appliesTo]
 * @property {boolean} [visible] // * @property {ITopViewGroup[]} [groups]
 */
