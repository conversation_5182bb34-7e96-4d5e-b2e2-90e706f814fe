import { utils } from 'c/utils';

/// <reference path="./bffInterprete.jsdoc.js" />

const AUTH_URL_SECURE = `interprete-web/v1/secure`;
const AUTH_URL_UNSECURE = `interprete-web/v1/unsecure`;

// const BASE_PATH = 'https://*************/api/pub';
const BASE_PATH = 'https://evo-dev.unipolsai.it/api/pub';
const BASE_PATH_MOCK = 'http://localhost:3000';
const NORM_PATH = 'https://*************/api/pub/indirizzi/v2/normalizza';
const BASE_PATH_BFF = 'https://tpd-vendita-ibrida-interprete-web-evo-dev.servizi.gr-u.it';

const POI_PATH = 'poiLocator/v1/bounds/poi';

/** @type {PEGA_API} */
export const PEGA_API = {
  loadPage: 0,
  nextPage: 1,
  updatePage: 2,
};

/** @type {IActionLoadRequestEnum} */
export const ACTION_LOAD_REQUEST = {
  create: 'CREATE',
  retrieve: 'retrieve',
  duplicate: 'duplicate',
  reload: 'reload',
};

export default class BffInterprete {
  _userLogged = false; // TODO: Implementare il recupero dello stato di login
  _mockUrls = false; // flag utilizzato per eseguire il mock degli url

  _assignmentId;
  _actionID;
  _caseID;

  constructor(config) {
    this.config = config;
    const paramsUrl = new URLSearchParams(window.location.search);
    this._mockUrls = paramsUrl.get('mockUrls') === 'true';
  }

  getURLS() {
    const basePath = this._mockUrls ? BASE_PATH_MOCK : BASE_PATH;
    const authURL = this._userLogged ? AUTH_URL_SECURE : AUTH_URL_UNSECURE;

    return {
      loadPage: `${basePath}/${authURL}/load-page`,
      nextPage: `${basePath}/${authURL}/go-next-page`,
      updatePage: `${basePath}/${authURL}/update-page`,
      getNormalizzationUrl: () => NORM_PATH, // Funzione per ottenere l'URL di normalizzazione
      getAllAgencies: `${basePath}/${POI_PATH}?category=AGENZIA`,
      getCloseAgencies: (lat, lng) =>
        `${basePath}/${POI_PATH}?category=AGENZIA&lat=${lat}&lng=${lng}&maxItem=5000`,
    };
  }

  getCustomHeaders() {
    return {
      'Content-Type': 'application/json',
      ...(this._mockUrls
        ? {
            'x-unipol-requestid': utils.generateRandomRequestId(),
          }
        : {
            'x-unipol-requestid': utils.generateRandomRequestId(),
            'x-ibm-client-id': 'c13117fd-af7e-45b6-8418-ff7b32a7422f', // sit
            'x-ibm-client-secret': 'bS5qE8iM2yW8eL0qM7fU7pI2pN2dK3yP0cM7oM3gI1xW0mG6bS', //sit
            // 'x-ibm-client-id': 'e6b5d646-485f-4e45-b937-752bf6ae84ee', // dev
            // 'x-ibm-client-secret': 'Y1iJ3cB1xU1gA5vD8lB4tB7yA4fY1lN8cS1sD5wQ6eN5dV5lL4', //dev
            // authorization:
            //   "Bearer eyJhbGciOiJSUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.foEQXUxY-Z2kuCm1cx1XYFCuQ9RyzXmMGSBt0lqe_-uwTT2HJTd4crlze61pNIU7AARpbwrbAkeouTouFN0eSwOa-LgUq8i7jh1sdgLHAfY4arXRQvpixqO5Tqt5TTf_Ttdf8Z0sa4cZu3CX0etMqWoqWjBwksLO570Y05Gsym0XU0V0lwfYVN4jph9V1tJCuyDAkA2RNo5o4VjyO-zYn6NL4vTDmn0WvqM9Uy1WAnULVdElUudHZkLCbpbQxVzWKc9_WfjDbrjSZO3eK1YjvWpTkVD1srLgfEQ1CZMflbOh6hKV25tqhKAZ7oq6wFyRjLKVHIKkB-v_w2seGAr9Lw"
          }),
    };
  }

  /**
   * @param {PegaAPI} api
   * @param {any} request
   * @param {string} [target]
   * @returns {Promise<any>}
   */
  async makeRequest(api, request, target) {
    const response = await this.apiMapper[api](request, target);

    if (response.status >= 400) {
      const error = response.error || 'Errore nella risposta API';
      throw { status: response.status, message: error, body: response };
    }

    switch (target) {
      case 'modalDialog':
        break;

      default:
        this._actionID = response?.metaBodyResponse?.actionId;
        this._assignmentId = response?.metaBodyResponse?.assignmentId;
        this._caseID = response?.pegaBodyResponse?.caseID;
        break;
    }

    return response;
  }

  /**
   * @typedef {Object} ApiMapper
   * @property {function(ILoadPageRequest): Promise<any>} [PEGA_API.loadPage] -
   * @property {function(IGoNextPageRequest): Promise<any>} [PEGA_API.nextPage]
   * @property {function(IUpdatePageRequest): Promise<any>} [PEGA_API.updatePage]
   */

  /** @type {ApiMapper} */
  apiMapper = {
    [PEGA_API.loadPage]: (request) => {
      return this.loadPage(request);
    },
    [PEGA_API.nextPage]: (request) => {
      return this.nextPage(request);
    },
    [PEGA_API.updatePage]: (request) => {
      return this.updatePage(request);
    },
  };

  /**
   * @typedef {Object} RequestMapper
   * @property {function(IActionLoadRequest, any): Promise<any>} [PEGA_API.loadPage]
   * @property {function(any): Promise<any>} [PEGA_API.nextPage]
   * @property {function(any): Promise<any>} [PEGA_API.updatePage]
   */

  /** @type {RequestMapper} */
  requestMapper = {
    [PEGA_API.loadPage]: (actionOnLoad, overrides) => {
      return {
        assignmentId: this._assignmentId,
        action: actionOnLoad,
        caseId: this._caseID,
        productType: this.config?.productType,
        env: this.config?.env,
        // retrieveType: this.retrieveType,
        // referencesToUpdate: this.directLogin
        //   ? {
        //       ...this._initData,
        //       ...this.processManagement,
        //       ...this.gestioneProcesso,
        //       ...this.VediEModifica
        //     }
        //   : this._initData,
        // idOfferta: this.idOfferta,
        // versione: this.versione,
        // env: this.env
        ...overrides,
      };
    },
    [PEGA_API.nextPage]: (overrides) => {
      return {
        assignmentId: this._assignmentId,
        actionId: this._actionID,
        productType: this.config?.productType,
        // captchaToken: this.statusService.captchaToken,
        // retrieveType: this.retrieveType,
        // referencesToUpdate: {
        //   ...this.formValue,
        //   ...this.processManagement,
        //   ...this.gestioneProcesso,
        //   ...this.VediEModifica
        // },
        // abTesting: this.abTesting,
        ...overrides,
      };
    },
    [PEGA_API.updatePage]: (overrides) => {
      return {
        assignmentId: this._assignmentId,
        actionId: this._actionID,
        productType: this.config?.productType,
        // referencesToUpdate: {
        //   ...this.formValue,
        //   ...this.processManagement,
        //   ...this.gestioneProcesso
        // },
        // refreshFor: this.refreshFor,
        ...overrides,
      };
    },
  };

  /**
   * @param {ILoadPageRequest} loadPageBody - The body of the request to load the page.
   * @returns {Promise<any>} A promise that resolves to the response of the API request.
   */
  async loadPage(loadPageBody) {
    const headers = {
      'x-unipol-canale': this._userLogged ? 'AR' : 'AP',
      ...this.getCustomHeaders(),
    };

    return utils.apiRequest('POST', this.getURLS().loadPage, headers, loadPageBody);
  }

  /**
   * @param {IGoNextPageRequest} nextPageBody - The body of the request to load the next page.
   * @returns {Promise<any>} A promise that resolves to the response of the API request.
   */
  async nextPage(nextPageBody) {
    const headers = {
      'x-unipol-canale': this._userLogged ? 'AR' : 'AP',
      ...this.getCustomHeaders(),
    };

    return utils.apiRequest('POST', this.getURLS().nextPage, headers, nextPageBody);
  }

  /**
   * @param {IUpdatePageRequest} updatePageBody - The body of the request to update the page.
   * @returns {Promise<any>} A promise that resolves to the response of the API request.
   */
  async updatePage(updatePageBody) {
    const headers = {
      'x-unipol-canale': this._userLogged ? 'AR' : 'AP',
      ...this.getCustomHeaders(),
    };

    return utils.apiRequest('POST', this.getURLS().updatePage, headers, updatePageBody);
  }
}
