import { LightningElement, api } from 'lwc';
import { utils } from 'c/utils';
import { utilsPegaText } from 'c/utilsPegaText';

export default class FlatCardLayout extends LightningElement {
    _groups;


    @api
    set groups(value) {
        this._groups = value;
    }

    get groups() {
        console.log(utils.printObject(this._groups), "groups")
        return this._groups;
    }


    get nomeAgenzia () {
        const caption = utils.getFirstCaptionByControlFormat(this.groups?.layout?.groups, 'NomeAgenzia');
        return utils.captionToValue(caption)?.value || ''; 
    }

    get indirizzoAgenzia () {
        const caption = utils.getFirstCaptionByControlFormat(this.groups?.layout?.groups, 'IndirizzoAgenzia');
        return utils.captionToValue(caption)?.value || ''; 
    }

    get stileNomeAgenzia () {
        return 'TEXT APP WHB16 WEB WHB16 WHB16 WHB16';
    }

    get stileIndirizzoAgenzia () {
        return 'TEXT APP WHB16 WEB WHM13 WHM13 WHM13';
    }

}
