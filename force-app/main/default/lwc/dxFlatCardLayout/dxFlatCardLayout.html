<template>
    <div class="FlatCardLayoutContainer">
        <div class="FlatCardLayoutContent">
            <template if:true={nomeAgenzia}>
                <c-custom-text-styles 
                content={nomeAgenzia} 
                text-css={stileNomeAgenzia}>
                </c-custom-text-styles>
            </template>

            <template if:true={indirizzoAgenzia}>
                <c-custom-text-styles 
                content={indirizzoAgenzia} 
                text-css={stileIndirizzoAgenzia}>
                </c-custom-text-styles>
            </template>
        </div>
        <span class="FlatCardLayoutCheck"></span>
    </div>
</template>