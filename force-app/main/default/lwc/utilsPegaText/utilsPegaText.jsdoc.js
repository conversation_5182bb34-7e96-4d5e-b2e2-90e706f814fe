/**
 * @typedef {Object} TextProps
 * @property {string} color
 * @property {string} weight
 * @property {string} [transformation]
 * @property {string} size
 * @property {string} [alignment]
 */

/**
 * @typedef {Object} TextConfig
 * @property {TextProps} desktop
 * @property {TextProps} tablet
 * @property {TextProps} mobile
 */

/**
 * @typedef {Object} TextCss
 * @property {CssElement} desktopCss
 * @property {CssElement} tabletCss
 * @property {CssElement} mobileCss
 */

/**
 * @typedef {Object} CssElement
 * @property {string} color
 * @property {string} font-size
 * @property {string} font-weight
 * @property {string} text-align
 * @property {string} [text-decoration]
 * @property {string} [text-transform]
 * @property {string} [font-style]
 */
