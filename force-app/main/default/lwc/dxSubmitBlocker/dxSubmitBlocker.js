import { LightningElement } from 'lwc';
import { addControlFormGroup, removeControlFormGroup } from 'c/formState';
import html from './dxSubmitBlocker.html';

export default class SubmitBlocker extends LightningElement {
  _selfUniqueId;

  connectedCallback() {
    this._selfUniqueId = `submit-blocker-${Date.now()}`;
    const formControl = { errors: { block: true } };
    addControlFormGroup(this._selfUniqueId, formControl);
  }

  disconnectedCallback() {
    removeControlFormGroup(this._selfUniqueId);
  }

  render() {
    return html;
  }
}
