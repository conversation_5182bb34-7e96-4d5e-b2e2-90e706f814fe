declare module "utilsActions" {
  /** Action types. */
  const ACTIONS: {
    SET_VALUE: string;
    POST_VALUE: string;
    REFRESH: string;
    PERFORM_ACTION: string;
    OPEN_URL: string;
    ADD_ROW: string;
    DELETE_ROW: string;
    LOCAL_ACTION: string;
    OPEN_ASSIGNMENT: string;
    FINISH_ASSIGNMENT: string;
  };

  /**
   * Retrieves the action data based on the specified field and target actions.
   *
   * @param field - The field object containing control and actionSets.
   * @param targetActions - An array of target action strings to filter the
   *   actions.
   * @returns An array of objects, each containing an action and its associated
   *   events.
   */
  function getActionData(
    field: {
      control?: {
        actionSets?: Array<{
          actions: Array<{ action: string }>;
          events: Array<{ event: string }>;
        }>;
      };
    },
    targetActions: string[]
  ): Array<{ action: string; events: Array<{ event: string }> }>;

  /**
   * Generates a list of actions based on the provided field and parent layout.
   *
   * @param field - The field object containing control and actionSets.
   * @param parentLayout - The parent layout object (not used in the current
   *   implementation).
   * @param handlers - An object containing handler functions.
   * @returns An array of objects, each containing a handler function and
   *   associated action data.
   */
  function getActionsList(
    field: {
      control?: {
        actionSets?: Array<{
          actions: Array<{ action: string; actionProcess?: any }>;
          events: Array<{ event: string }>;
        }>;
      };
    },
    parentLayout: object,
    handlers: { handleLocalAction: Function }
  ): Array<{ handler: Function; data: any }>;

  /** Utility actions. */
  export const utilsActions: {
    ACTIONS: typeof ACTIONS;
    getActionsList: typeof getActionsList;
  };
}
