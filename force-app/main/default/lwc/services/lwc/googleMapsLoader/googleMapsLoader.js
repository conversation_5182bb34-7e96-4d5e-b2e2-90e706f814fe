let apiLoaded = false;
let apiLoadingPromise = null;

export function loadGoogleMapsApi(apiKey) {
    if (apiLoaded) return Promise.resolve(true);
    if (apiLoadingPromise) return apiLoadingPromise;
    apiLoadingPromise = new Promise((resolve, reject) => {
        if (window.google && window.google.maps && window.google.maps.places) {
            apiLoaded = true;
            resolve(true);
            return;
        }
        const script = document.createElement('script');
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
        script.async = true;
        script.onload = () => {
            apiLoaded = true;
            resolve(true);
        };
        script.onerror = reject;
        document.head.appendChild(script);
    });
    return apiLoadingPromise;
}