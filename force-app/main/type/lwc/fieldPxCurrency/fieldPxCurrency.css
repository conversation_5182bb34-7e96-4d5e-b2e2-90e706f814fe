/* @font-face {
  font-family: 'Unipol Bold';
  src: url("/resource/unipolFonts/Apex_New_Bold.woff") format('woff');
  font-style: normal;
  font-weight: bold;
} */

.pxCurrency {
  width: 100%;
}

.debug {
  border: 1px solid #1fd6e7;
}

.temporaryLabel {
  color: #1fd6e7;
  font-style: italic;
}

.px-currency-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: flex-start;
  gap: 4px;
}

.input-field-container {
  position: relative;
}

.input-field {
  width: 100%;
  height: 48px;
  padding: 12px 16px;
  border: 1px solid var(--color-darker);
  color: var(--middle-grey-pu);
  -moz-appearance: textfield;
}

.input-field::-webkit-outer-spin-button, .input-field::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.Text-responsive-bold,
.NomeGaranzia,
.PrezzoGaranzia,
.Text-responsive-L-bold {
  font-family: var(--font-family-bold);
  /* font-family: 'Unipol Bold'; */
  color: var(--main_color);
  font-weight: bold;
}

@media (min-width: 1025px) {
  .Text-responsive-bold,
  .NomeGaranzia,
  .PrezzoGaranzia,
  .Text-responsive-L-bold {
    font-size: var(--font-text-responsive-desktop-size);
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .Text-responsive-bold,
  .NomeGaranzia,
  .PrezzoGaranzia,
  .Text-responsive-L-bold {
    font-size: var(--font-text-responsive-tablet-size);
  }
}

@media (max-width: 767px) {
  .Text-responsive-bold,
  .NomeGaranzia,
  .PrezzoGaranzia,
  .Text-responsive-L-bold {
    font-size: var(--font-text-responsive-mobile-size);
  }
}




.icon-Attenzione-pieno {
  color: var(--alert-color);
  font-size: 40px;
}

.bd-icona {
  font-size: 22px;
  color: #ff001f;
  top: 5px;
  position: relative;
  margin-right: 5px;
}

.invalid-input {
  border: 1px solid red !important;
}

.testo-non-valido {
  height: 14px;
  font-family: var(--font-family-medium);
  font-size: 14px;
  font-stretch: normal;
  font-style: normal;
  line-height: 0.88;
  letter-spacing: normal;
  color: #ff001f;
}

.error-message {
  display: flex;
  align-items: center;
  height: 30px;
}