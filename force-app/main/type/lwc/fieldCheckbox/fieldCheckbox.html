<template>
  <div class={componentClass}>
    <p if:true={debug} class="temporaryLabel">FIELD PX CHECKBOX:</p>
    <p if:true={debug} class="temporaryLabel">format: {format}</p>

    <!-- Qui usiamo solo il containerClass per tutte le classi -->
    <div class={containerClass} onclick={handleInputChange}>
      <!-- <div class="us-checkbox-wrapper-checkmark"> -->
      <input type="checkbox" style="display: none;" name={field.reference} checked={isChecked} value={isChecked}
        disabled={disabled} readonly={readonly} data-reference={field.reference} />
      <span class={checkboxClass}></span>
      <!-- </div> -->

      <template if:true={label}>
        <div class="us-checkbox-label">
          <c-custom-text-styles content={label} text-css={field.labelFormat}></c-custom-text-styles>
          <template if:true={subtitle}>
            <p class="text-responsive-italic">{subtitle}</p>
          </template>
        </div>
      </template>
    </div>
  </div>
</template>