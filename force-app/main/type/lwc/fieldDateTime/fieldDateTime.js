import { getFormState, markFieldTouched, setFieldValue } from 'c/formState';
import { fireEvent } from 'c/pubsub';
import { utils } from 'c/utils';
import { LightningElement, api } from 'lwc';

export default class FieldDateTime extends LightningElement {
  _field;

  @api
  decodedValue;

  @api parentLayout;
  @api debug;

  @api
  get field() {
    return this._field;
  }

  set field(value) {
    this._field = value;

    setFieldValue(
      value.reference,
      value.value,
      value.type === 'date'
        ? value?.customAttributes?.validation || 'date'
        : value?.customAttributes?.validation || 'time',
      value.required || value?.customAttributes?.required
    );
  }

  get label() {
    return this.field?.label ? utils.decodeHTML(this.field.label) : '';
  }

  get required() {
    return this.field.required === true;
  }

  get readonly() {
    return this.field.readOnly === true;
  }

  get disabled() {
    return this.field.disabled;
  }

  get formatType() {
    return this.field.type == 'Date' ? 'date' : 'time';
  }

  get componentClass() {
    return `pxDateTime ${this.debug ? 'debug' : ''}`.trim();
  }

  get labelFormat() {
    return this.field.labelFormat;
  }

  get formattedTime() {
    return this.formatType === 'date'
      ? this.decodedValue
      : this.decodedValue.substring(0, 2) + ':' + this.decodedValue.substring(2, 4);
  }

  handleInputChange(evt) {
    markFieldTouched(this.field.reference);

    const container = this.template.querySelector('lightning-input');
    const state = getFormState();
    const reference = this.field?.reference;
    const touched = state.touchedFields.has(reference);

    if (!touched) return;

    const isRequired = state.requiredFields.has(reference);
    const isTouched = state.touchedFields.has(reference);
    const value = state.values[reference]?.value ?? '';

    if (isRequired && isTouched && value === '') {
      container.classList.add('invalid-input');
    } else {
      container.classList.remove('invalid-input');
    }

    fireEvent('handleFieldChanged', {
      evt: evt,
      field: this.field,
      parentLayout: this.parentLayout,
    });
  }
}
