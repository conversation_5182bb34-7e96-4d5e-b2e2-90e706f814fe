.pxDropdown {
    width: 100%;
}
  
.debug {
    border: 1px solid #e7a11f;
}

.temporaryLabel {
    color: #e7a11f;
    font-style: italic;
}

.visible-desktop {
    display: none;
  
    @media (min-width: 1025px) {
      display: block;
    }
  }
  
.visible-tablet {
    display: none;

    @media (min-width: 768px) and (max-width: 1024px) {
        display: block;
    }
}

.visible-mobile {
    display: none;

    @media (max-width: 767px) {
        display: block;
    }
}

.us-input-fake {
  /* block-size: 44px;
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  background-color: #fff;
  width: 100%;
  padding: 6px 10px;
  border: 1px solid rgb(25, 58, 86);
  height: 44px;
  text-overflow: ellipsis;
  color: #5c5c5c;
  font-family: var(--font-family-default);
  font-weight: bold; */
  max-width: 500px;
  position: relative;
  font-family: var(--font-family-default);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.25;
  height: 48px;
}

.us-input-fake.isDisabled {
  cursor: default !important;
}

.select-styled {
  color: #5c5c5c !important;
  position: absolute;
  inset: 0;
  background-color: #fff;
  padding: 6px 12px !important;
  border: 1px solid var(--color-darker);
  text-align: left;
  word-break: break-word;
  overflow: hidden;
  display: flex;
  align-items: center;
  height: 48px;
  font-family: var(--font-family-medium);
}

.select-styled.isDisabled {
  pointer-events: none;
  color: #9b9b9b;
  border: 0;
}

.select-styled.disabled-input-box {
  background-color: #f1f1f1 !important;
  color: #9b9b9b !important;
  font-size: 16px;
  font-family: Unipol;
  line-height: 24px;
  height: 48px !important;
  pointer-events: none;
  cursor: not-allowed;
}

.select-styled::after {
  border: none !important;
  transform: rotate(135deg) !important;
  width: 11px !important;
  cursor: pointer;
  display: inline-block;
  height: 11px !important;
  border-style: solid !important;
  border-width: 2px 2px 0 0 !important;
  position: absolute;
  top: 17px !important;
  right: 17px !important;
  content: "";
}

.select-styled.active::after {
  transform: rotate(-45deg) !important;
  top: 19px !important;
}

.us-input-fake > .valueContent {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  margin-right: 20px;
}

.us-input-fake > .us-select-wrapper {
  margin: 0;
  list-style: none;
  padding: 0;
  border: 1px solid rgb(15, 50, 80);
  font-family: var(--font-family-default);
  font-size: 16px;
  display: none;
  position: absolute;
  top: 43px;
  left: -1px;
  z-index: 10;
  background-color: #fff;
  width: calc(100% + 2px);
  overflow: auto;
}

.us-input-fake > .us-select-wrapper, .withLabel {
  /*top: 73px;*/
  top: 33px;
}

.opened {
  display: block !important;
}

/* .active {
  color: #0f3250;
  font-family: var(--font-family-bold);
  border: 2px solid #e2f0f9 !important;
} */

.us-input-fake > .us-select-wrapper > .us-option-wrapper {
  border-bottom: 1px solid #f1f1f1;
  padding: 10px;
  cursor: pointer;
}

.us-input-fake > .us-select-wrapper > .us-option-wrapper:hover{
  background-color: #f1f1f1;
}

.icon-Attenzione-pieno {
  color: var(--alert-color);
  font-size: 40px;
}

.bd-icona {
  font-size: 22px;
  color: #ff001f;
  top: 5px;
  position: relative;
  margin-right: 5px;
}

.invalid-input {
  border: 1px solid red;
}

.testo-non-valido {
  height: 14px;
  font-family: var(--font-family-medium);
  font-size: 14px;
  font-stretch: normal;
  font-style: normal;
  line-height: 0.88;
  letter-spacing: normal;
  color: #ff001f;
}