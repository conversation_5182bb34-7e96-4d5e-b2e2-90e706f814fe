<template>
  <div class={componentClass}>
    <p if:true={debug} class="temporaryLabel">DROPDOWN FIELD:</p>
    <p if:true={debug} class="temporaryLabel">format: {format}</p>
    <p if:true={debug} class="temporaryLabel">labelFormat: {labelFormat}</p>

    <template if:true={showLikeLabel}>
      <c-custom-text-styles content={selectedOptionValue} text-css={labelFormat}></c-custom-text-styles>
    </template>

    <template if:false={showLikeLabel}>
      <c-custom-text-styles content={field.label} text-css={labelFormat}></c-custom-text-styles>
      <div class="tpd_selectAngular us-input-fake" onclick={dropdownClick} onblur={fieldTouched} tabindex="0">
        <div class={dropdownClasses}>
          <span class="valueContent">{selectedOptionValue}</span>
          <template if:false={opened}>
            <div class="dropdown-icon">
              <i class="icon-Freccia-down"></i>
            </div>
          </template>
          <template if:true={opened}>
            <div class="dropdown-icon">
              <i class="icon-Freccia-up"></i>
            </div>
          </template>
        </div>

        <template if:true={opened}>
          <ul class={listClasses} style="max-height: 250px;">
            <template for:each={options} for:item="option">
              <li key={option.value} data-key={option.value} class={option.className} onclick={handleOptionSelect}>
                {option.label}
              </li>
            </template>
          </ul>
        </template>
      </div>
      <template if:false={isValid}>
        <div class="error-message">
          <i class="icon-Attenzione-pieno bd-icona"></i>
          <span class="testo-non-valido">{errorMessage}</span>
        </div>
      </template>
    </template>
  </div>
</template>