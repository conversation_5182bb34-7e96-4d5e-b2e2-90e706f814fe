import { utils } from 'c/utils';
import { utilsPegaText } from 'c/utilsPegaText';
import { LightningElement, api } from 'lwc';

export default class FieldPxInteger extends LightningElement {
  _field;

  @api
  decodedValue;

  @api debug;

  @api
  get field() {
    return this._field;
  }

  set field(value) {
    this._field = value;
    this.handleFieldChange();
  }

  get isPercentuale() {
    return this._field.fieldID ? this._field.fieldID.toLowerCase().includes('percentuale') : false;
  }

  get format() {
    if (this._field.labelFormat) return utils.getClassFromFormat(this._field.labelFormat);
  }

  get labelFormat() {
    return this.field.labelFormat;
  }

  get percentageValue() {
    return `${this.decodedValue} ${this.isPercentuale ? '%' : ''}`;
  }

  get componentClass() {
    return `pxInteger ${this.debug ? 'debug' : ''}`.trim();
  }

  handleFieldChange() {
    // console.log("[TEST] field", utils.printObject(this.field));
  }
}
