import { errors } from 'c/errorMessage';
import {
  checkPatternForType,
  getFormState,
  markFieldTouched,
  patterns,
  setFieldValue,
} from 'c/formState';
import { utilsPega } from 'c/utilsPega';

import { fireEvent } from 'c/pubsub';
import { utils } from 'c/utils';
import { LightningElement, api, track } from 'lwc';

export default class FieldInputText extends LightningElement {
  _field;

  @api decodedValue = '';
  @api parentLayout;
  @api debug;

  @track isValid = true;
  @track errorMessage = '';


  @api
  get field() {
    return this._field;
  }

  set field(value) {
    this._field = value;
    const isEffectivelyVisible = value && value.visible !== false;
    const isPegaRequired = value && (value.required === true || (value.customAttributes?.required === 'true'));
    const effectiveIsRequired = isEffectivelyVisible && isPegaRequired;

    setFieldValue(
      value?.reference,
      value?.value,
      value?.customAttributes?.validation || 'text',
      effectiveIsRequired
    );
  }

  connectedCallback() {
    this.registerValidationListener();
  }

  disconnectedCallback() {
    if (this._field && this._field.reference) {
      setFieldValue(
        this._field.reference,
        undefined,
        this._field.customAttributes?.validation || 'text',
        false
      );
    } else {
    }
    document.removeEventListener('triggerValidation', this.handleExternalValidation);
  }
  get label() {
    return this.field?.label ? utils.decodeHTML(this.field.label) : '';
  }

  get format() {
    if (this.field.labelFormat) return utils.getClassFromFormat(this.field.labelFormat);
  }

  get labelFormat() {
    return this.field.labelFormat;
  }

  get tooltip() {
    let tooltip = '';
    if (this.field.control.modes?.[0]?.tooltip) {
      tooltip = this.field.control.modes[0].tooltip;
    }
    return utils.decodeHTML(tooltip);
  }

  get placeholder() {
    let placeholder;
    placeholder = this.field?.control?.modes?.[0]?.placeholder;
    return placeholder;
  }

  get readonly() {
    return this.field.readOnly === true;
  }

  get isDisabled() {
    return this.field.disabled;
  }

  get formatReadWritetType() {
    let type = 'text';
    let pattern = null;
    let errorMessage = '';
    const state = getFormState();
    const reference = this.field?.reference;
    const isRequired = state.requiredFields.has(reference);
    const isTouched = state.touchedFields.has(reference);
    const value = state.values[reference]?.value ?? '';

    if (isRequired && isTouched && value === '') {
      errorMessage = errors['required'];
      return { type, pattern, errorMessage };
    }

    if (!this.field.control || !this.field.control.modes[0].formatType) return { type, pattern };

    const formatType = this.field?.customAttributes?.validation || 'text';
    const taxCode = this.field?.customAttributes?.referenceCF;

    const { errorMessage: validationErrorMessage } = checkPatternForType('', formatType);

    errorMessage = validationErrorMessage;

    if ((formatType === 'nameWithCF' || formatType === 'surnameWithCF') && taxCode && value) {
      const cfType = formatType === 'nameWithCF' ? 'name' : 'surname';
      const isValid = !utilsPega.customAttributes.checkNameSurnameOnFiscalCode(
        taxCode,
        value,
        cfType
      );
      if (!isValid) {
        errorMessage = errors[cfType === 'name' ? 'errorNameCF' : 'errorSurnameCF'];
      }
    }

    const inputTypes = {
      number: 'number',
      houseNumber: 'text',
      email: 'text',
      mobilephone: 'tel',
      taxCode: 'text',
    };

    type = inputTypes[formatType] || 'text';
    pattern = patterns[formatType];

    return { type, pattern, errorMessage };
  }

  get componentClass() {
    return `pxTextInput ${this.debug ? 'debug' : ''}`.trim();
  }

  get textInputClass() {
    return `input-dati-utente ${this.field.disabled ? 'disable-input' : ''} ${this.isValid ? '' : 'invalid-input'}`.trim();
  }

  handleInputChange(evt) {
    this.decodedValue = evt.target.value;

    setFieldValue(
      this.field.reference,
      evt.target.value,
      this.field?.customAttributes?.validation || 'text',
      this.field.required || this.field?.customAttributes?.required
    );

    this.validateInput();

    fireEvent('handleFieldChanged', {
      evt: evt,
      field: this.field,
      parentLayout: this.parentLayout,
    });
  }

  fieldTouched() {
    markFieldTouched(this.field.reference);

    const container = this.template.querySelector('.input-dati-utente');
    const state = getFormState();
    const reference = this.field?.reference;
    const touched = state.touchedFields.has(reference);

    if (!touched) return;

    const isRequired = state.requiredFields.has(reference);
    const isTouched = state.touchedFields.has(reference);
    const value = state.values[reference]?.value ?? '';

    if (isRequired && isTouched && value === '') {
      container.classList.add('invalid-input');
      this.isValid = false;
    } else {
      container.classList.remove('invalid-input');
      this.isValid = true;
    }
  }

  validateInput() {
    const state = getFormState();
    const reference = this.field?.reference;
    const value = state.values[reference]?.value ?? '';
    const isRequired = state.requiredFields.has(reference);
    const isTouched = state.touchedFields.has(reference);

    const container = this.template.querySelector('.input-dati-utente');

    let isValid = true;
    let errorMessage = '';

    if (isRequired && isTouched && value === '') {
      isValid = false;
      errorMessage = errors['required'];
    } else {
      const formatType = this.field?.customAttributes?.validation || 'text';
      const taxCode = this.field?.customAttributes?.referenceCF;

      const { errorMessage: validationErrorMessage } = checkPatternForType(value, formatType);

      if (validationErrorMessage) {
        isValid = false;
        errorMessage = validationErrorMessage;
      }

      if ((formatType === 'nameWithCF' || formatType === 'surnameWithCF') && taxCode && value) {
        const cfType = formatType === 'nameWithCF' ? 'name' : 'surname';
        const match = utilsPega.customAttributes.checkNameSurnameOnFiscalCode(
          taxCode,
          value,
          cfType
        );

        if (!match) {
          isValid = false;
          errorMessage = errors[cfType === 'name' ? 'errorNameCF' : 'errorSurnameCF'];
        }
      }
    }

    this.isValid = isValid;
    this.errorMessage = errorMessage;

    if (container) {
      container.classList.toggle('invalid-input', !isValid);
    }

    return isValid;
  }

  registerValidationListener() {
    document.addEventListener('triggerValidation', this.handleExternalValidation);
  }

  handleExternalValidation = () => {
    this.fieldTouched();
    this.validateInput();
  };
}