import { fireEvent } from 'c/pubsub';
import { utils } from "c/utils";
import { utilsPega } from "c/utilsPega";
import { LightningElement, api } from "lwc";

export default class FieldLink extends LightningElement {
  _field;

  @api
  decodedValue;

  @api
  disabled;

  @api 
  debug;

  @api
  get field() {
    return this._field;
  }

  set field(value) {
    this._field = value;
    this.handleFieldChange();
  }

  get format() {
    return this.field.control.format;
  }

  get label() {
    const value = this.field.control?.label
      ? this.field.control.label
      : this.field.value;

    return value ? utils.decodeHTML(value) : "";
  }

  get tooltip() {
    let tooltip = "";

    if (this.field?.control?.modes?.length > 1) {
      if (
        // this.field.control.type === FIELD_TYPES.BUTTON ||
        this.field.control.type === utilsPega.fields.FIELD_TYPES.link
        // ||
        // this.field.control.type === FIELD_TYPES.ICON
      ) {
        if (this.field.control.modes[1].tooltip) {
          tooltip = this.field.control.modes[1].tooltip;
        }
      } else {
        if (this.field.control.modes[0].tooltip) {
          tooltip = this.field.control.modes[0].tooltip;
        }
      }
    }
    return utils.decodeHTML(tooltip);
  }

  get componentClass() {
    return `link ${this.debug ? "debug" : ""}`.trim();
  }

  handleClick = (evt) => {
    // TODO: test
    if (this.field.customAttributes) {
      if (
        this.field.customAttributes.linkType === "_URL" &&
        this.field.customAttributes.link
      ) {
        const target = this.field.customAttributes.Target
          ? this.field.customAttributes.Target
          : "_blank";

        window.open(this.field.customAttributes.link, target);
      }
    } else {
      if (
        evt.preventDefault &&
        this.field.control.actionSets &&
        this.field.control.actionSets.length > 0
      ) {
        evt.preventDefault();
      }

      fireEvent('handleFieldClicked', {
        evt: evt, 
        field: this.field, 
        parentLayout: this.parentLayout
      });
    }
  };

  handleFieldChange() {
    // console.log("[TEST] field", utils.printObject(this.field));
  }
}
