<template>
  <div class={componentClass}>
    <p if:true={debug} class="temporaryLabel">FIELDCAPTION: {format}</p>
    <div class="color-link" style="cursor: pointer;" data-reference={field.reference} onclick={handleClick}>
      <template if:true={disabled}>
        <span>{label}</span>
      </template>
      <template if:false={disabled}>
        <c-custom-text-styles content={label} text-css={field.labelFormat}></c-custom-text-styles>
      </template>
    </div>
  </div>
</template>