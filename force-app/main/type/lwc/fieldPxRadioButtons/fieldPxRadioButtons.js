import { fireEvent } from 'c/pubsub';
import { utils } from 'c/utils';
import { LightningElement, api } from 'lwc';

export default class FieldRadioButtons extends LightningElement {
  _field;
  _radioButtonStyle;

  @api options = [];
  @api decodedValue;
  @api disabled;
  @api parentLayout;
  @api debug;
  @api layoutFrazionamentoAnnuale;
  @api layoutFrazionamentoMensile;
  clickedOption;

  @api
  get field() {
    return this._field;
  }

  set field(value) {
    this._field = value;
    this.clickedOption = value.value;
    this._radioButtonStyle = value.customAttributes.style;
  }

  get radioButtonStyle() {
    return this._radioButtonStyle;
  }

  get options() {
    if(!this.isRadioCardFrazionamento){
      return (
        this.field?.control?.modes[0]?.options?.map((opt, index) => ({
          label: opt.value,
          value: opt.key,
          checked: opt.key === this.clickedOption,
          subtitle: this.field.customAttributes.subtitle?.[index] ?? null,
        })) || []
      );
    } else {
      const blockedIds = this.field.customAttributes.IdOpzioneBlockList.split(",");
      return (
        this.field?.control?.modes[0]?.options
          ?.filter((opt) => !blockedIds.includes(opt.key))
          .map((opt, index) => ({
            label: opt.value,
            value: opt.key,
            checked: opt.key === this.clickedOption,
            subtitle: this.field.customAttributes.subtitle?.[index] ?? null,
            isMensile: opt.key === '9',
            isAnnuale: opt.key === '1',
          })) || []
      );
    }
  }

  get label() {
    return this.field?.control.label ? utils.decodeHTML(this.field.control.label) : '';
  }

  get format() {
    if (this.field.labelFormat) return utils.getClassFromFormat(this.field.labelFormat);
  }

  get required() {
    return this.field.required === true;
  }

  get componentClass() {
    return `pxRadioButtons ${this.debug ? 'debug' : ''} container-radio radio-${this.field.control.modes[0].orientation === 'vertical' ? 'vertical' : 'horizontal'}`.trim();
  }

  get isFlatCardInverted() {
    return this.radioButtonStyle === 'FlatCardInverted' || '';
  }

  get isPackage() {
    return this.radioButtonStyle === 'Package' || '';
  }

  get isGreenCheck() {
    return this.radioButtonStyle === 'GreenCheck' || '';
  }

  get isRadioCard() {
    return this.radioButtonStyle === 'RadioCard' || '';
  }

  get isFlatCatd() {
    return this.radioButtonStyle === 'FlatCard' || '';
  }

  get isRadioCardFrazionamento() {
    return this.field?.customAttributes['GestioneProcesso.FrazionamentoSceltoDaUtente'] === 'true' || '';
  }

  get labelFormatClass() {
    return `mrg-label-input`.trim();
  }

  get greenCheckSubtitle() {
    return this.field.subtitle && this.subtitle[0];
  }

  get subtitleFormat() {
    return this.field.customAttributes.subtitleFormat;
  }

  get titleFormat(){
    return this.field.customAttributes.titleFormat;
  }

  get titleFormatSelected(){
    return this.field.customAttributes.titleFormatSelected;
  }

  get containerClass() {
    const direction = this.field.radioOrientation?.toLowerCase() === 'horizontal'
      ? 'horizontal-layout'
      : 'vertical-layout';
    return 'FlatCardContainer ' + direction;
  }

  isOptionMensile(key) {
    return key === '9';
  }

  isOptionAnnuale(key) {
    return key === '1';
  }

  handleInputChange(evt) {
    const input = evt.currentTarget.querySelector('input[type="radio"]');
    if (!input) return;

    const clickedKey = input.dataset.value || input.value;
    this.clickedOption = clickedKey;

    const customEvent = {
      ...evt,
      target: {
        value: clickedKey,
        reference: this.field.reference,
      },
    };

    fireEvent('handleFieldChanged', {
      evt: customEvent,
      field: this.field,
      parentLayout: this.parentLayout,
    });
  }
}
