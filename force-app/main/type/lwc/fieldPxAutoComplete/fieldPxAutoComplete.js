import { errors } from 'c/errorMessage';
import { setFieldValue, markFieldTouched } from 'c/formState';
import { fireEvent, registerListener, unregisterListener } from 'c/pubsub';
import { utils } from 'c/utils';
import { LightningElement, api, track } from 'lwc';

export default class FieldPxAutoComplete extends LightningElement {
  _field;
  @track opened = false;
  @track isValid = true;
  visualizedValue = '';
  visualizedImage = '';
  @api decodedValue;
  @api parentLayout;
  @api debug;
  @api disabled;

  @api
  get field() {
    return this._field;
  }

  set field(value) {
    this._field = value;

    const selectedOption = value.control.modes[0].options.find((opt) => opt.key === value.value);
    this.visualizedValue = selectedOption ? selectedOption?.value : '';

    setFieldValue(
      value.reference,
      value.value,
      value?.customAttributes?.validation || 'text',
      value.required || value?.customAttributes?.required
    );
  }

  get label() {
    return this.field?.label ? utils.decodeHTML(this.field.label) : '';
  }

  get placeholder() {
    return this.field?.control?.modes?.[0]?.placeholder || '';
  }

  get componentClass() {
    return `pxAutoComplete ${this.debug ? 'debug' : ''}`.trim();
  }

  get isReadonly() {
    return this.field.readOnly === true;
  }

  get labelFormat() {
    return this.field.labelFormat;
  }

  get selectedImage() {
    return `background-image: url("https://evo-dev.unipolsai.it/NextAssets/interprete-pu/immagini-pet/${this.visualizedImage}.png"`;
  }

  get options() {
    const allOptions = this.field?.control?.modes[0]?.options || [];
    const filteredOptions = this.visualizedValue
      ? allOptions.filter((opt) =>
        opt.value.toLowerCase().includes(this.visualizedValue.toLowerCase())
      )
      : allOptions;

    return (
      filteredOptions.map((opt) => ({
        label: opt.value,
        value: opt.key,
        tooltip: opt.tooltip
          ? `background-image: url("https://evo-dev.unipolsai.it/NextAssets/interprete-pu/immagini-pet/${opt.tooltip}.png"`
          : undefined,
      })) || allOptions
    );
  }

  get autoCompleteClasses() {
    return `tpd_selectAngular ${this.disabled ? 'isDisabled disabled-input-box' : ''} `;
  }

  get listOptionsClasses() {
    return `select-options withLabel ${this.opened ? 'opened' : ''} `;
  }

  get listClasses() {
    return `select-styled ${this.disabled ? 'isDisabled disabled-input-box' : ''}`;
  }

  get errorMessage() {
    if (!this.isValid) {
      return errors['required'];
    }
    return '';
  }

  dropdownClick() {
    const container = this.template.querySelector('.select-styled');

    if (this.opened) {
      this.opened = false;
      container.classList.remove('active');
    } else {
      this.opened = true;
      container.classList.add('active');
    }
  }

  fieldTouched() {
    this.opened = !this.opened;
    markFieldTouched(this.field.reference);

    const container = this.template.querySelector('.select-styled');
    const state = getFormState();
    const reference = this.field?.reference;
    const touched = state.touchedFields.has(reference);

    if (!touched) return;

    const isRequired = state.requiredFields.has(reference);
    const isTouched = state.touchedFields.has(reference);
    const value = state.values[reference]?.value ?? '';

    if (isRequired && isTouched && value === '') {
      container.classList.add('invalid-input');
      this.isValid = false;
    } else {
      container.classList.remove('invalid-input');
      this.isValid = true;
    }
  }

  handleInputChange(evt) {
    if (this.field.readOnly || this.field.disabled) return;
    // Update UI value and state
    const newValue = evt.target.value;
    this.visualizedValue = newValue;

    // Find selected option by matching label
    const selectedOption = this.options.find(
      (opt) => opt.label.toLowerCase() === newValue.toLowerCase()
    );

    if (!selectedOption) return;

    // If we have a valid option, trigger the event
    const syntheticEvent = {
      target: {
        dataset: {
          reference: this.field.reference,
        },
        value: selectedOption.value,
      },
    };

    fireEvent('handleFieldChanged', {
      evt: syntheticEvent,
      field: this.field,
      parentLayout: this.parentLayout,
    });
    this.validateField();
  }

  handleOptionSelect(evt) {
    const selectedKey = evt.currentTarget.dataset.key;
    const selectedOption = this.options.find((opt) => opt.value === selectedKey);

    if (selectedOption) {
      this.visualizedValue = selectedOption.label;
      this.visualizedImage = selectedOption?.tooltip;
      this.opened = false;

      // Create synthetic change event with selected value
      const changeEvent = {
        target: {
          value: selectedOption.label,
        },
      };

      // Trigger handleInputChange with updated value
      this.handleInputChange(changeEvent);
    }
  }

  validateField() {
    const container = this.template.querySelector('.select-styled');
    if (!container) {
      return;
    }

    if (container.classList.contains('disabled-input-box')) {
      return;
    }

    if (this.field.required && !this.visualizedValue) {
      container.classList.add('invalid-input');
      this.isValid = false;
    } else {
      container.classList.remove('invalid-input');
      this.isValid = true;
    }
  }

  handleExternalValidation() {
    try {
      this.validateField();
    } catch (error) {
    }
  }

  connectedCallback() {
    registerListener('triggerValidation', this.handleExternalValidation, this);
  }

  disconnectedCallback() {
    unregisterListener('triggerValidation', this.handleExternalValidation, this);
    this._isRegistered = false;
  }

  renderedCallback() {
    if (this._isRegistered) return;

    this._isRegistered = true;
    registerListener('triggerValidation', this.handleExternalValidation, this);
  }
}